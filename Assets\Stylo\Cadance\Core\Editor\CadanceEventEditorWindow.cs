using UnityEngine;
using UnityEditor;
using System;
using System.Collections.Generic;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Editor window for visualizing and editing Cadance events.
    /// </summary>
    public class CadanceEventEditorWindow : EditorWindow
    {
        // Constants
        private const float TIMELINE_HEIGHT = 100f;
        private const float EVENT_HEIGHT = 20f;
        private const float ZOOM_MIN = 0.1f;
        private const float ZOOM_MAX = 5.0f;

        // Editor state
        private Vector2 scrollPosition;
        private float zoomLevel = 1.0f;
        private float viewportOffset = 0f;
        private bool showBeatMarkers = true;
        private bool showEventLabels = true;
        private bool playbackMode = false;
        private float currentPlaybackTime = 0f;
        private double lastUpdateTime;

        // Selected events
        private List<CadanceEvent> selectedEvents = new List<CadanceEvent>();

        // Sample events for demonstration
        private List<CadanceEvent> sampleEvents = new List<CadanceEvent>();
        private float totalDuration = 60f; // 60 seconds of timeline

        public static void ShowWindow()
        {
            var window = GetWindow<CadanceEventEditorWindow>("Cadance Events");
            window.minSize = new Vector2(600, 300);
            window.GenerateSampleEvents();
        }

        private void GenerateSampleEvents()
        {
            // Generate sample events for demonstration
            sampleEvents.Clear();

            // Add regular beat events (every 0.5 seconds)
            for (float time = 0; time < totalDuration; time += 0.5f)
            {
                sampleEvents.Add(new CadanceEvent("Beat", (int)(time * 44100), (time % 2 < 0.1f) ? "1" : ""));
            }

            // Add measure events (every 2 seconds)
            for (float time = 0; time < totalDuration; time += 2.0f)
            {
                sampleEvents.Add(new CadanceEvent("Measure", (int)(time * 44100), (time / 2.0f).ToString("0")));
            }

            // Add some gameplay events
            AddGameplayEvent(3.5f, "EnemySpawn", "Basic");
            AddGameplayEvent(7.2f, "EnemySpawn", "Advanced");
            AddGameplayEvent(11.5f, "PowerUp", "Speed");
            AddGameplayEvent(15.0f, "Transition", "Phase2");
            AddGameplayEvent(20.5f, "EnemySpawn", "Boss");
            AddGameplayEvent(30.0f, "Transition", "Ending");
        }

        private void AddGameplayEvent(float time, string eventId, string payload)
        {
            sampleEvents.Add(new CadanceEvent(eventId, (int)(time * 44100), payload));
        }

        private void OnEnable()
        {
            lastUpdateTime = EditorApplication.timeSinceStartup;
            EditorApplication.update += OnEditorUpdate;
        }

        private void OnDisable()
        {
            EditorApplication.update -= OnEditorUpdate;
        }

        private void OnEditorUpdate()
        {
            // Update playback time
            if (playbackMode)
            {
                double deltaTime = EditorApplication.timeSinceStartup - lastUpdateTime;
                currentPlaybackTime += (float)deltaTime;

                // Ensure we're always showing the playback head
                EnsurePlaybackHeadVisible();

                // Force repaint to update playback head position
                Repaint();
            }

            lastUpdateTime = EditorApplication.timeSinceStartup;
        }

        private void EnsurePlaybackHeadVisible()
        {
            float playbackPixelPos = TimeToPixel(currentPlaybackTime);
            float viewportWidth = position.width;

            if (playbackPixelPos < viewportOffset ||
                playbackPixelPos > viewportOffset + viewportWidth)
            {
                // Center playback head in viewport
                viewportOffset = playbackPixelPos - (viewportWidth / 2);
                viewportOffset = Mathf.Max(0, viewportOffset);
            }
        }

        private void OnGUI()
        {
            DrawToolbar();

            EditorGUILayout.Space();

            // Main timeline area
            Rect timelineRect = GUILayoutUtility.GetRect(position.width, TIMELINE_HEIGHT);
            DrawTimeline(timelineRect);

            EditorGUILayout.Space();

            DrawEventList();

            // Status bar
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            EditorGUILayout.LabelField($"Zoom: {zoomLevel:0.00}x", GUILayout.Width(100));
            EditorGUILayout.LabelField($"Time: {currentPlaybackTime:0.00}s", GUILayout.Width(100));
            EditorGUILayout.LabelField($"Events: {sampleEvents.Count}", GUILayout.Width(100));
            EditorGUILayout.EndHorizontal();
        }

        private void DrawToolbar()
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);

            // Playback controls
            if (GUILayout.Button(playbackMode ? "■" : "▶", EditorStyles.toolbarButton, GUILayout.Width(30)))
            {
                playbackMode = !playbackMode;
            }

            if (GUILayout.Button("◀◀", EditorStyles.toolbarButton, GUILayout.Width(30)))
            {
                currentPlaybackTime = 0;
            }

            // Zoom controls
            GUILayout.FlexibleSpace();

            EditorGUI.BeginChangeCheck();
            zoomLevel = EditorGUILayout.Slider(zoomLevel, ZOOM_MIN, ZOOM_MAX, GUILayout.Width(200));
            if (EditorGUI.EndChangeCheck())
            {
                // Adjust viewport offset to maintain center focus when zooming
                float centerTime = PixelToTime(viewportOffset + (position.width / 2));
                viewportOffset = TimeToPixel(centerTime) - (position.width / 2);
                viewportOffset = Mathf.Max(0, viewportOffset);
            }

            showBeatMarkers = GUILayout.Toggle(showBeatMarkers, "Beat Markers", EditorStyles.toolbarButton);
            showEventLabels = GUILayout.Toggle(showEventLabels, "Event Labels", EditorStyles.toolbarButton);

            EditorGUILayout.EndHorizontal();
        }

        private void DrawTimeline(Rect rect)
        {
            // Store current viewport for scrolling
            float oldViewportOffset = viewportOffset;

            // Handle scrolling
            Event current = Event.current;
            if (rect.Contains(current.mousePosition))
            {
                if (current.type == EventType.ScrollWheel)
                {
                    viewportOffset += current.delta.y * 10;
                    viewportOffset = Mathf.Max(0, viewportOffset);
                    current.Use();
                }

                // Middle-mouse drag for panning
                if (current.type == EventType.MouseDrag && current.button == 2)
                {
                    viewportOffset -= current.delta.x;
                    viewportOffset = Mathf.Max(0, viewportOffset);
                    current.Use();
                }
            }

            // Draw timeline background
            GUI.Box(rect, "", EditorStyles.helpBox);

            // Calculate time range visible in viewport
            float startTime = PixelToTime(viewportOffset);
            float endTime = PixelToTime(viewportOffset + rect.width);
            float visibleDuration = endTime - startTime;

            // Draw time markers
            DrawTimeMarkers(rect, startTime, endTime);

            // Draw events
            DrawEvents(rect);

            // Draw playback head
            if (playbackMode || currentPlaybackTime > 0)
            {
                float playheadX = TimeToPixel(currentPlaybackTime) - viewportOffset;

                if (playheadX >= 0 && playheadX <= rect.width)
                {
                    Color oldColor = Handles.color;
                    Handles.color = Color.red;
                    Handles.DrawLine(
                        new Vector3(rect.x + playheadX, rect.y, 0),
                        new Vector3(rect.x + playheadX, rect.y + rect.height, 0)
                    );
                    Handles.color = oldColor;

                    // Draw current time label
                    GUIStyle timeStyle = new GUIStyle(EditorStyles.miniLabel);
                    timeStyle.normal.textColor = Color.red;
                    GUI.Label(new Rect(rect.x + playheadX + 2, rect.y + 2, 60, 20),
                        $"{currentPlaybackTime:0.00}s", timeStyle);
                }
            }

            // Handle click to set playback position
            if (current.type == EventType.MouseDown && current.button == 0 && rect.Contains(current.mousePosition))
            {
                float clickedTime = PixelToTime(viewportOffset + (current.mousePosition.x - rect.x));
                currentPlaybackTime = Mathf.Clamp(clickedTime, 0, totalDuration);
                current.Use();
                Repaint();
            }
        }

        private void DrawTimeMarkers(Rect rect, float startTime, float endTime)
        {
            GUIStyle timeStyle = new GUIStyle(EditorStyles.miniLabel);
            timeStyle.normal.textColor = Color.gray;

            // Calculate appropriate marker interval based on zoom
            float markerInterval = 1.0f; // 1 second default

            if (zoomLevel < 0.2f) markerInterval = 10.0f;
            else if (zoomLevel < 0.5f) markerInterval = 5.0f;
            else if (zoomLevel < 0.8f) markerInterval = 2.0f;
            else if (zoomLevel > 2.0f) markerInterval = 0.5f;
            else if (zoomLevel > 4.0f) markerInterval = 0.1f;

            // Calculate first visible marker
            float firstMarker = Mathf.Floor(startTime / markerInterval) * markerInterval;

            // Draw markers
            for (float time = firstMarker; time <= endTime; time += markerInterval)
            {
                float xPos = TimeToPixel(time) - viewportOffset;

                // Major marker
                Color oldColor = Handles.color;
                Handles.color = new Color(0.5f, 0.5f, 0.5f, 0.5f);
                Handles.DrawLine(
                    new Vector3(rect.x + xPos, rect.y, 0),
                    new Vector3(rect.x + xPos, rect.y + rect.height, 0)
                );
                Handles.color = oldColor;

                // Time label
                GUI.Label(new Rect(rect.x + xPos + 2, rect.y + rect.height - 15, 50, 15),
                    $"{time:0.0}s", timeStyle);
            }

            // Draw minor markers (beat markers)
            if (showBeatMarkers && zoomLevel >= 0.5f)
            {
                float beatInterval = markerInterval / 4.0f;
                float firstBeat = Mathf.Floor(startTime / beatInterval) * beatInterval;

                for (float time = firstBeat; time <= endTime; time += beatInterval)
                {
                    // Skip if this is already a major marker
                    if (Mathf.Approximately(time % markerInterval, 0))
                        continue;

                    float xPos = TimeToPixel(time) - viewportOffset;

                    Color oldColor = Handles.color;
                    Handles.color = new Color(0.5f, 0.5f, 0.5f, 0.2f);
                    Handles.DrawLine(
                        new Vector3(rect.x + xPos, rect.y + rect.height * 0.25f, 0),
                        new Vector3(rect.x + xPos, rect.y + rect.height * 0.75f, 0)
                    );
                    Handles.color = oldColor;
                }
            }
        }

        private void DrawEvents(Rect rect)
        {
            foreach (var evt in sampleEvents)
            {
                float time = evt.SampleTime / 44100.0f; // Convert samples to seconds
                float xPos = TimeToPixel(time) - viewportOffset;

                // Check if event is visible
                if (xPos < 0 || xPos > rect.width)
                    continue;

                // Calculate vertical position based on event type
                float yPos = rect.y + GetEventYOffset(evt.EventID);

                // Get color for this event type
                Color eventColor = GetEventColor(evt.EventID);

                // Draw event marker
                DrawEventMarker(rect, xPos, yPos, eventColor, evt);
            }
        }

        private void DrawEventMarker(Rect timelineRect, float xPos, float yPos, Color eventColor, CadanceEvent evt)
        {
            Rect markerRect = new Rect(
                timelineRect.x + xPos - 5,
                yPos - 5,
                10, 10);

            Color oldColor = GUI.color;
            GUI.color = eventColor;

            // Draw different shapes based on event type
            switch (evt.EventID)
            {
                case "Beat":
                    GUI.DrawTexture(markerRect, EditorGUIUtility.whiteTexture);
                    break;
                case "Measure":
                    // Draw a diamond shape instead of using the non-existent triangleTexture
                    GUI.DrawTexture(markerRect, EditorGUIUtility.whiteTexture);
                    // For Measure events, we could use a different color to distinguish them
                    break;
                default:
                    GUI.DrawTexture(markerRect, EditorGUIUtility.whiteTexture);
                    break;
            }

            // Draw event label if enabled
            if (showEventLabels)
            {
                GUIStyle labelStyle = new GUIStyle(EditorStyles.miniLabel);
                labelStyle.normal.textColor = eventColor;

                string labelText = evt.EventID;
                string payloadText = evt.Payload?.ToString() ?? "";
                if (!string.IsNullOrEmpty(payloadText))
                {
                    labelText += $" [{payloadText}]";
                }

                GUI.Label(new Rect(
                    timelineRect.x + xPos + 7,
                    yPos - 8,
                    150, 16),
                    labelText, labelStyle);
            }

            GUI.color = oldColor;

            // Check for event selection
            Event current = Event.current;
            if (current.type == EventType.MouseDown &&
                current.button == 0 &&
                markerRect.Contains(current.mousePosition))
            {
                if (current.shift)
                {
                    // Add to selection
                    if (!selectedEvents.Contains(evt))
                    {
                        selectedEvents.Add(evt);
                    }
                }
                else
                {
                    // New selection
                    selectedEvents.Clear();
                    selectedEvents.Add(evt);
                }

                current.Use();
            }
        }

        private float GetEventYOffset(string eventID)
        {
            // Position different event types at different heights
            switch (eventID)
            {
                case "Beat":
                    return 20;
                case "Measure":
                    return 40;
                case "EnemySpawn":
                    return 60;
                case "PowerUp":
                    return 70;
                case "Transition":
                    return 80;
                default:
                    return 50;
            }
        }

        private Color GetEventColor(string eventID)
        {
            // Different colors for different event types
            switch (eventID)
            {
                case "Beat":
                    return new Color(0.2f, 0.6f, 1.0f);
                case "Measure":
                    return new Color(1.0f, 0.5f, 0.2f);
                case "EnemySpawn":
                    return Color.red;
                case "PowerUp":
                    return Color.green;
                case "Transition":
                    return Color.magenta;
                default:
                    return Color.white;
            }
        }

        private void DrawEventList()
        {
            EditorGUILayout.LabelField("Selected Events", EditorStyles.boldLabel);

            if (selectedEvents.Count == 0)
            {
                EditorGUILayout.HelpBox("No events selected. Click on an event marker to select it.", MessageType.Info);
                return;
            }

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            foreach (var evt in selectedEvents)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Event ID:", GUILayout.Width(80));
                GUILayout.Label(evt.EventID, EditorStyles.boldLabel);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Payload:", GUILayout.Width(80));
                GUILayout.Label(evt.Payload?.ToString() ?? "");
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Time:", GUILayout.Width(80));
                float timeInSeconds = evt.SampleTime / 44100.0f;
                GUILayout.Label($"{timeInSeconds:0.000}s ({evt.SampleTime} samples)");
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndScrollView();
        }

        // Helper functions for time/pixel conversions
        private float TimeToPixel(float time)
        {
            return time * 100.0f * zoomLevel;
        }

        private float PixelToTime(float pixel)
        {
            return pixel / (100.0f * zoomLevel);
        }
    }
}
