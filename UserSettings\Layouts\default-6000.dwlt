%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12004, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PixelRect:
    serializedVersion: 2
    x: 0
    y: 23
    width: 1920
    height: 1009
  m_ShowMode: 0
  m_Title: Game
  m_RootView: {fileID: 4}
  m_MinSize: {x: 200, y: 226}
  m_MaxSize: {x: 4000, y: 4026}
  m_Maximized: 1
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12004, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PixelRect:
    serializedVersion: 2
    x: 1920
    y: 46
    width: 1920
    height: 986
  m_ShowMode: 4
  m_Title: Project Auditor
  m_RootView: {fileID: 5}
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_Maximized: 1
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: GameView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1920
    height: 1009
  m_MinSize: {x: 200, y: 226}
  m_MaxSize: {x: 4000, y: 4026}
  m_ActualView: {fileID: 17}
  m_Panes:
  - {fileID: 17}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 3}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1920
    height: 1009
  m_MinSize: {x: 200, y: 226}
  m_MaxSize: {x: 4000, y: 4026}
  vertical: 0
  controlID: 62
  draggingID: 0
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12008, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 6}
  - {fileID: 8}
  - {fileID: 7}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1920
    height: 986
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_UseTopView: 1
  m_TopViewHeight: 36
  m_UseBottomView: 1
  m_BottomViewHeight: 20
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12011, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1920
    height: 36
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
  m_LastLoadedLayoutName: 
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12042, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 966
    width: 1920
    height: 20
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 9}
  - {fileID: 14}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 36
    width: 1920
    height: 930
  m_MinSize: {x: 300, y: 100}
  m_MaxSize: {x: 24288, y: 16192}
  vertical: 0
  controlID: 124
  draggingID: 0
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 10}
  - {fileID: 13}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1272
    height: 930
  m_MinSize: {x: 200, y: 100}
  m_MaxSize: {x: 16192, y: 16192}
  vertical: 1
  controlID: 125
  draggingID: 0
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 11}
  - {fileID: 12}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1272
    height: 534
  m_MinSize: {x: 200, y: 50}
  m_MaxSize: {x: 16192, y: 8096}
  vertical: 0
  controlID: 126
  draggingID: 0
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneHierarchyWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 370
    height: 534
  m_MinSize: {x: 201, y: 226}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 18}
  m_Panes:
  - {fileID: 18}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ProjectAuditorWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 370
    y: 0
    width: 902
    height: 534
  m_MinSize: {x: 412, y: 666}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 15}
  m_Panes:
  - {fileID: 19}
  - {fileID: 16}
  - {fileID: 15}
  m_Selected: 2
  m_LastSelected: 1
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ConsoleWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 534
    width: 1272
    height: 396
  m_MinSize: {x: 101, y: 126}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 21}
  m_Panes:
  - {fileID: 20}
  - {fileID: 21}
  - {fileID: 22}
  m_Selected: 1
  m_LastSelected: 0
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: InspectorWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1272
    y: 0
    width: 648
    height: 930
  m_MinSize: {x: 276, y: 76}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 23}
  m_Panes:
  - {fileID: 23}
  - {fileID: 24}
  - {fileID: 25}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b8a3392a722874051babc255daf94eff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 410, y: 640}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Project Auditor
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project Auditor\u200B"
  m_Pos:
    serializedVersion: 2
    x: 371
    y: 24
    width: 900
    height: 508
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_AreaSelectionSummary: 
  m_AssemblyNames: []
  m_AssemblySelectionSummary: 
  m_Report:
    m_Version: 0.2
    SessionInfo:
      Categories: 
      m_Platform: -2
      AssemblyNames: []
      CodeOptimization: 1
      CompilationMode: 0
      DiagnosticParams:
        m_ParamsStack:
        - PlatformGroup: 0
          m_SerializedParams:
          - Key: SpriteAtlasEmptySpaceLimit
            Value: 50
          - Key: TextureStreamingMipmapsSizeLimit
            Value: 4000
          - Key: StreamingClipThresholdBytes
            Value: 218294
          - Key: LongDecompressedClipThresholdBytes
            Value: 204800
          - Key: LongCompressedMobileClipThresholdBytes
            Value: 204800
          - Key: LoadInBackGroundClipSizeThresholdBytes
            Value: 204800
          - Key: StreamingAssetsFolderSizeLimit
            Value: 50
        CurrentParamsIndex: 0
      ProjectAuditorVersion: 
      UnityVersion: 
      CompanyName: 
      ProjectId: 
      ProjectName: 
      ProjectRevision: 
      DateTime: 
      HostName: 
      HostPlatform: 
      UseRoslynAnalyzers: 0
    DisplayName: 
    NeedsSaving: 0
    m_ModuleInfos: []
    m_DescriptorLibrary:
      m_SerializedDescriptors:
      - Id: PAA0005
        Title: 'Texture: Solid color is not 1x1 size'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The texture is a single, solid color and is bigger than 1x1
          pixels in size. Redundant texture data occupies memory unnecessarily.
        Recommendation: Consider shrinking the texture to 1x1 size.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA0006
        Title: 'Texture: Solid color is not 1x1 size'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The texture is a single, solid color and is bigger than 1x1
          pixels in size. Redundant texture data occupies memory unnecessarily.
        Recommendation: Consider shrinking the texture to 1x1 size.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA0007
        Title: 'Texture Atlas: Too much empty space'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The texture atlas contains a lot of empty space. Empty space
          contributes to texture memory usage.
        Recommendation: Consider reorganizing your texture atlas in order to reduce
          the amount of empty space.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA0000
        Title: 'Texture: Mipmaps not enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 136
        Platforms: 
        Description: <b>Generate Mip Maps</b> in the Texture Import Settings is not
          enabled. Using textures that are not mipmapped in a 3D environment can
          impact rendering performance and introduce aliasing artifacts.
        Recommendation: Consider enabling mipmaps using the <b>Advanced > Generate
          Mip Maps</b> option in the Texture Import Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA0001
        Title: 'Texture: Mipmaps enabled on Sprite/UI texture'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 129
        Platforms: 
        Description: <b>Generate Mip Maps</b> is enabled in the Texture Import Settings
          for a Sprite/UI texture. This might reduce rendering quality of sprites
          and UI.
        Recommendation: Consider disabling mipmaps using the <b>Advanced > Generate
          Mip Maps</b> option in the texture inspector. This will also reduce your
          build size.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA0002
        Title: 'Texture: Read/Write enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The <b>Read/Write Enabled</b> flag in the Texture Import Settings
          is enabled. This causes the texture data to be duplicated in memory.
        Recommendation: If not required, disable the <b>Read/Write Enabled</b> option
          in the Texture Import Settings.
        DocumentationUrl: https://docs.unity3d.com/Manual/class-TextureImporter.html
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA0003
        Title: 'Texture: Mipmaps Streaming not enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 192
        Platforms: 
        Description: The <b>Streaming Mipmaps</b> option in the Texture Import Settings
          is not enabled. As a result, all mip levels for this texture are loaded
          into GPU memory for as long as the texture is loaded, potentially resulting
          in excessive texture memory usage.
        Recommendation: Consider enabling the <b>Streaming Mipmaps</b> option in
          the Texture Import Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA2000
        Title: 'Shader: Not compatible with SRP batcher'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: The shader is not compatible with SRP Batcher.
        Recommendation: Consider adding SRP Batcher compatibility to the shader.
          This will reduce the CPU time Unity requires to prepare and dispatch draw
          calls for materials that use the same shader variant.
        DocumentationUrl: https://docs.unity3d.com/Manual/SRPBatcher.html
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA0008
        Title: 'Sprite Atlas: Too much empty space'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The Sprite Atlas texture contains a lot of empty space. Empty
          space contributes to texture memory usage.
        Recommendation: Consider reorganizing your Sprite Atlas Texture in order
          to reduce the amount of empty space.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4000
        Title: 'Audio: Long AudioClip is not set to Streaming'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The AudioClip has a runtime memory footprint larger than the
          streaming buffer size of 200KB, but its <b>Load Type</b> is not set to
          <b>Streaming</b>. Storing the whole clip in memory rather than streaming
          it may be an inefficient use of memory.
        Recommendation: Consider setting <b>Load Type</b> to <b>Streaming</b> in
          the AudioClip Import Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4001
        Title: 'Audio: Short AudioClip is set to streaming'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The AudioClip has a runtime memory footprint smaller than the
          streaming buffer size of 200KB, but its <b>Load Type</b> is set to <b>Streaming</b>.
          Requiring a streaming buffer for this clip is an inefficient use of memory.
        Recommendation: Set <b>Load Type</b> to <b>Compressed in Memory</b> or <b>Decompress
          On Load</b> in the AudioClip Import Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4003
        Title: 'Audio: AudioClip is stereo'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 192
        Platforms: 
        Description: The audio source asset is in stereo, <b>Force To Mono</b> is
          not enabled in the AudioClip Import Settings, and the <b>Load Type</b>
          is not <b>Streaming</b>, which implies the AudioClip may be used as a diagetic
          positional sound effect. Positional effects should be mono; only non-diagetic
          music and effects should be stereo.
        Recommendation: Tick the <b>Force To Mono</b> checkbox in the AudioClip Import
          Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4004
        Title: 'Audio: AudioClip is set to Decompress On Load'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 96
        Platforms: 
        Description: The AudioClip is long, and its <b>Load Type</b> is set to <b>Decompress
          On Load</b>. The clip's memory footprint may be excessive, and decompression
          may impact load times.
        Recommendation: Consider setting the <b>Load Type</b> to <b>Compressed In
          Memory</b> or <b>Streaming</b>. If you have concerns about the CPU cost
          of decompressing <b>Compressed In Memory</b> clips for playback, consider
          a format which is fast to decompress, such as <b>ADPCM</b>.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4005
        Title: 'Audio: Compressed AudioClip is Compressed In Memory'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: The AudioClip's <b>Load Type</b> is set to <b>Compressed In
          Memory</b> but the clip is imported with a format that is not trivial to
          decompress. Decompression will be performed every time the clip is played,
          and may impact CPU performance.
        Recommendation: If runtime performance is impacted, either set the <b>Load
          Type</b> to <b>Decompress On Load</b> or set the <b>Compression Format</b>
          to <b>ADPCM</b>, which is fast to decompress.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4007
        Title: 'Audio: Sample Rate is over 48 kHz'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 97
        Platforms: 
        Description: The AudioClip's source sample rate is higher than 48 kHz, and
          the <b>Sample Rate Setting</b> does not override it. Most Blu-Rays are
          at 48kHz, and higher sample rates are generally only used during the recording
          process or for scientific data. If compression is applied during the import
          process the sample rate gets capped at 48kHz. If compression isn't applied,
          the runtime memory footprint for this clip will be excessive. In both cases,
          the source file size is excessive.
        Recommendation: Set the <b>Sample Rate Setting</b> to <b>Override</b> and
          the <b>Sample Rate</b> to <b>48000</b> Hz or lower.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4008
        Title: 'Audio: Preload Audio Data is enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 32
        Platforms: 
        Description: The <b>Preload Audio Data</b> checkbox is ticked for this AudioClip.
          This forces scene/prefab loading to wait synchronously until the AudioClip
          has completed loading before continuing running, and can impact scene load/initialization
          times.
        Recommendation: Consider un-ticking the <b>Preload Audio Data</b> checkbox.
          Audio preloading is only required when the AudioClip must play at the exact
          moment the scene begins simulating, or if the audio timing must be very
          precise the first time it is played.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4009
        Title: 'Audio: Load In Background is not enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 36
        Platforms: 
        Description: This AudioClip is large, and the <b>Load In Background</b> checkbox
          is not ticked. Loading will be performed synchronously and will block the
          main thread. This may impact load times or create CPU spikes, depending
          on when the clip is loaded.
        Recommendation: Tick the <b>Load In Background</b> checkbox in the AudioClip
          Import Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4010
        Title: 'Audio: Compression Format is MP3'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 128
        Platforms: 
        Description: The AudioClip's <b>Compression Format</b> is set to <b>MP3</b>.
          MP3 is an old compression format which has been surpassed in efficiency
          and quality by newer formats such as Vorbis.
        Recommendation: Set the <b>Compression Format</b> to <b>Vorbis</b> in the
          AudioClip's Import Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA4011
        Title: 'Audio: Source asset is in a lossy compressed format'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 128
        Platforms: 
        Description: The file format used by the source asset for the AudioClip uses
          a lossy compression format. The Asset Import process decompresses the audio
          data and recompresses it in the chosen runtime format. This may result
          in a further loss of sound quality.
        Recommendation: Wherever possible, select a lossless file format such as
          .WAV or .AIFF for source assets.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC2000
        Title: Boxing Allocation
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: Boxing happens where a value type, such as an integer, is converted
          into an object of reference type. This causes an allocation on the managed
          heap.
        Recommendation: Try to avoid boxing when possible. Create methods and APIs
          that can accept value types.
        DocumentationUrl: https://learn.microsoft.com/en-us/dotnet/csharp/programming-guide/types/boxing-and-unboxing
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0192
        Title: Debug.Log / Debug.LogFormat
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Debug.Log</b> methods take a lot of CPU time, especially
          if used frequently.
        Recommendation: Remove logging code, or strip it from release builds by using
          scripting symbols for conditional compilation (#if ... #endif) or the <b>ConditionalAttribute</b>
          on a custom logging method that calls Debug.Log. Where logging is required
          in release builds, CPU times can be reduced by disabling stack traces in
          log messages. You can do this by setting <b>Application.SetStackTraceLogType(LogType.Log,
          StackTraceLogType.None)</b>.
        DocumentationUrl: https://docs.unity3d.com/Manual/UnderstandingPerformanceGeneralOptimizations.html
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0193
        Title: Debug.LogWarning / Debug.LogWarningFormat
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Debug.LogWarning</b> methods take a lot of CPU time, especially
          if used frequently.
        Recommendation: Remove logging code, or strip it from release builds by using
          scripting symbols for conditional compilation (#if ... #endif) or the <b>ConditionalAttribute</b>
          on a custom logging method that calls Debug.LogWarning. Where logging is
          required in release builds, CPU times can be reduced by disabling stack
          traces in log messages. You can do this by setting <b>Application.SetStackTraceLogType(LogType.Log,
          StackTraceLogType.None)</b>.
        DocumentationUrl: https://docs.unity3d.com/Manual/UnderstandingPerformanceGeneralOptimizations.html
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0001
        Title: UnityEngine.WebCamTexture.GetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>WebCamTexture.GetPixels()</b> allocates managed memory.
        Recommendation: Use WebCamTexture.GetPixels32() instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0002
        Title: UnityEngine.WebCamTexture.GetPixels32
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>WebCamTexture.GetPixels32()</b> allocates managed memory
          if a suitable array is not provided as a parameter.
        Recommendation: Ensure that you pass an array of <b>Color32[]</b> to this
          API method for it to fill out, to avoid creating a new array every time
          the method is called.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0003
        Title: UnityEngine.GeometryUtility.CalculateFrustumPlanes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: Some versions of <b>GeometryUtility.CalculateFrustumPlanes()</b>
          allocate managed memory.
        Recommendation: Ensure that you use the <b>CalculateFrustumPlanes(Matrix4x4
          worldToProjectionMatrix, Plane[] planes)</b> version of this API method,
          in order to be able to pass a pre-allocated Array of Planes.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0004
        Title: UnityEngine.Resources.FindObjectsOfTypeAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Resources.FindObjectsOfTypeAll()</b> allocates managed memory.
        Recommendation: Use <b>Resources.FindObjectsOfTypeNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0005
        Title: UnityEngine.Texture2D.GetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Texture2D.GetPixels()</b> allocates managed memory.
        Recommendation: Use <b>Texture2D.GetRawTextureData()</b> instead. This method
          returns a NativeArray of pixel data, and so does not allocate managed memory.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0006
        Title: UnityEngine.Texture2D.GetPixels32
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Texture2D.GetPixels32()</b> allocates managed memory.
        Recommendation: Use <b>Texture2D.GetRawTextureData()</b> instead. This method
          returns a NativeArray of pixel data, and so does not allocate managed memory.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0007
        Title: UnityEngine.Rigidbody.SweepTestAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Rigidbody.SweepTestAll()</b> allocates managed memory.
        Recommendation: Use <b>Rigidbody.SweepTestNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0008
        Title: UnityEngine.Physics.RaycastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics.RaycastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics.RaycastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0009
        Title: UnityEngine.Physics.CapsuleCastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics.CapsuleCastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics.CapsuleCastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0010
        Title: UnityEngine.Physics.SphereCastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics.SphereCastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics.SphereCastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0011
        Title: UnityEngine.Physics.BoxCastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics.BoxCastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics.BoxCastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0012
        Title: UnityEngine.Physics.OverlapCapsule
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics.OverlapCapsule()</b> allocates managed memory.
        Recommendation: Use <b>Physics.OverlapCapsuleNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0013
        Title: UnityEngine.Physics.OverlapSphere
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics.OverlapSphere()</b> allocates managed memory.
        Recommendation: Use <b>Physics.OverlapSphereNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0014
        Title: UnityEngine.Physics.OverlapBox
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics.OverlapBox()</b> allocates managed memory.
        Recommendation: Use <b>Physics.OverlapBoxNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0015
        Title: UnityEngine.Physics2D.LinecastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.LinecastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.LinecastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0016
        Title: UnityEngine.Physics2D.RaycastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.RaycastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.RaycastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0017
        Title: UnityEngine.Physics2D.CircleCastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.CircleCastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.CircleCastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0018
        Title: UnityEngine.Physics2D.BoxCastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.BoxCastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.BoxCastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0019
        Title: UnityEngine.Physics2D.CapsuleCastAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.CapsuleCastAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.CapsuleCastNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0020
        Title: UnityEngine.Physics2D.GetRayIntersectionAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.GetRayIntersectionAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.GetRayIntersectionNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0021
        Title: UnityEngine.Physics2D.OverlapPointAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.OverlapPointAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.OverlapPointNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0022
        Title: UnityEngine.Physics2D.OverlapCircleAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.OverlapCircleAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.OverlapCircleNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0023
        Title: UnityEngine.Physics2D.OverlapBoxAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.OverlapBoxAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.OverlapBoxNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0024
        Title: UnityEngine.Physics2D.OverlapAreaAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.OverlapAreaAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.OverlapAreaNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0025
        Title: UnityEngine.Physics2D.OverlapCapsuleAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Physics2D.OverlapCapsuleAll()</b> allocates managed memory.
        Recommendation: Use <b>Physics2D.OverlapCapsuleNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0026
        Title: UnityEngine.Component.GetComponentsInChildren
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Component.GetComponentsInChildren()</b> allocates managed
          memory.
        Recommendation: Ensure you are using one of the versions of <b>GameObject.GetComponentsInChildren()</b>
          which accepts a List<T> as a parameter and populates it with the components
          it finds.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0027
        Title: UnityEngine.Component.GetComponentsInParent
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Component.GetComponentsInParent()</b> allocates managed memory.
        Recommendation: Ensure you are using one of the versions of <b>GameObject.GetComponentsInParent()</b>
          which accepts a List<T> as a parameter and populates it with the components
          it finds.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0028
        Title: UnityEngine.GameObject.GetComponentsInChildren
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: Some versions of <b>GameObject.GetComponentsInChildren()</b>
          allocate managed memory.
        Recommendation: Ensure you are using one of the versions of <b>GameObject.GetComponentsInChildren()</b>
          which accepts a List<T> as a parameter and populates it with the components
          it finds.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0029
        Title: UnityEngine.GameObject.GetComponentsInParent
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: Some versions of <b>GameObject.GetComponentsInParent()</b> allocate
          managed memory.
        Recommendation: Ensure you are using one of the versions of <b>GameObject.GetComponentsInParent()</b>
          which accepts a List<T> as a parameter and populates it with the components
          it finds.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0030
        Title: UnityEngine.Collider.OnTriggerStay
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>OnTriggerStay()</b> methods can detrimentally affect performance
          if they perform a lot of processing or if there are many Colliders which
          implement this method.
        Recommendation: Profile CPU performance to look for bottlenecks, examine
          the contents of all <b>OnTriggerStay()</b> methods, and consider refactoring
          code to not use them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0031
        Title: UnityEngine.MonoBehaviour.OnTriggerStay
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>OnTriggerStay()</b> methods can detrimentally affect performance
          if they perform a lot of processing or if there are many MonoBehaviours
          which implement this method.
        Recommendation: Profile CPU performance to look for bottlenecks, examine
          the contents of all <b>OnTriggerStay()</b> methods, and consider refactoring
          code to not use them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0032
        Title: UnityEngine.Collider2D.OnTriggerStay2D
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>OnTriggerStay2D()</b> methods can detrimentally affect performance
          if they perform a lot of processing or if there are many Collider2Ds which
          implement this method.
        Recommendation: Profile CPU performance to look for bottlenecks, examine
          the contents of all <b>OnTriggerStay2D()</b> methods, and consider refactoring
          code to not use them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0033
        Title: UnityEngine.MonoBehaviour.OnTriggerStay2D
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>OnTriggerStay2D()</b> methods can detrimentally affect performance
          if they perform a lot of processing or if there are many MonoBehaviours
          which implement this method.
        Recommendation: Profile CPU performance to look for bottlenecks, examine
          the contents of all <b>OnTriggerStay2D()</b> methods, and consider refactoring
          code to not use them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0034
        Title: UnityEngine.Collider.OnCollisionStay
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>OnCollisionStay()</b> methods can detrimentally affect performance
          if they perform a lot of processing or if there are many Colliders which
          implement this method.
        Recommendation: Profile CPU performance to look for bottlenecks, examine
          the contents of all <b>OnCollisionStay()</b> methods, and consider refactoring
          code to not use them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0035
        Title: UnityEngine.MonoBehaviour.OnCollisionStay
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>OnCollisionStay()</b> methods can detrimentally affect performance
          if they perform a lot of processing or if there are many MonoBehaviours
          which implement this method.
        Recommendation: Profile CPU performance to look for bottlenecks, examine
          the contents of all <b>OnCollisionStay()</b> methods, and consider refactoring
          code to not use them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0036
        Title: UnityEngine.Rigidbody.OnCollisionStay
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>OnCollisionStay()</b> methods can detrimentally affect performance
          if they perform a lot of processing or if there are many RigidBodies which
          implement this method.
        Recommendation: Profile CPU performance to look for bottlenecks, examine
          the contents of all <b>OnCollisionStay()</b> methods, and consider refactoring
          code to not use them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0037
        Title: UnityEngine.ImageConversion.LoadImage
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>ImageConversion.LoadImage()</b> defaults to maintaining a
          CPU-accessible copy of the image. This is a waste of memory if not needed.
        Recommendation: If a CPU-accessible copy of the texture is not required,
          ensure that the markNonReadable flag passed into the method is set to true.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0039
        Title: UnityEngine.Renderer.material
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 8
        Platforms: 
        Description: The <b>Renderer.material</b> property creates a unique copy
          of the Renderer's material. This breaks draw call batching and results
          in a higher number of draw calls, impacting rendering performance.
        Recommendation: If possible, use <b>Renderer.sharedMaterial</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0051
        Title: UnityEngine.ComputeBuffer.GetData
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>ComputeBuffer.GetData()</b> stalls the CPU until the GPU
          has finished accessing the buffer. This can lead to significant CPU performance
          problems.
        Recommendation: Avoid reading back from ComputeBuffers if it is at all possible.
          If it's unavoidable, profile your project carefully and regularly to monitor
          the performance impact.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0052
        Title: UnityEngine.Texture2D.SetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Texture2D.SetPixels()</b> is slower than <b>Texture2D.SetPixels32()</b>.
        Recommendation: Use <b>Texture2D.SetPixels32()</b>, <b>Texture2D.GetRawTextureData()</b>,
          or <b>Texture2D.Apply()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0053
        Title: UnityEngine.Texture3D.SetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Texture3D.SetPixels()</b> is slower than <b>Texture3D.SetPixels32()</b>.
        Recommendation: Use <b>Texture3D.SetPixels32()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0054
        Title: UnityEngine.Texture2DArray.SetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Texture2DArray.SetPixels()</b> is slower than <b>Texture2DArray.SetPixels32()</b>.
        Recommendation: Use <b>Texture2DArray.SetPixels32()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0055
        Title: UnityEngine.CubemapArray.SetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>CubemapArray.SetPixels()</b> is slower than <b>CubemapArray.SetPixels32()</b>.
        Recommendation: Use <b>CubemapArray.SetPixels32()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0056
        Title: UnityEngine.GameObject.SendMessage
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>GameObject.SendMessage()</b> is a very slow and CPU-intensive
          method.
        Recommendation: Implement a custom system to replace SendMessage - get the
          components you want to send messages to and call methods directly on them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0057
        Title: UnityEngine.Component.SendMessage
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Component.SendMessage()</b> is a very slow and CPU-intensive
          method.
        Recommendation: Implement a custom system to replace SendMessage - get the
          components you want to send messages to and call methods directly on them.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0058
        Title: UnityEngine.MonoBehaviour.OnGUI
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>OnGUI()</b> is used by the legacy Immediate Mode GUI (IMGUI),
          which is extremely CPU-intensive. If a single OnGUI() method is present
          in a project's code, IMGUI will initialize and consume CPU time.
        Recommendation: Remove all <b>OnGUI()</b> methods from the project code.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0059
        Title: UnityEngine.AI.NavMeshPath.corners
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>AI.NavMeshPath.corners</b> allocates managed
          memory.
        Recommendation: Use <b>AI.NavMeshPath.GetCornersNonAlloc()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0060
        Title: UnityEngine.Animator.parameters
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property Animator.parameters allocates managed memory.
        Recommendation: Use <b>Animator.GetParameter()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0061
        Title: UnityEngine.Animations.ParentConstraint.translationOffsets
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Animations.ParentConstraint.translationOffsets</b>
          allocates managed memory.
        Recommendation: Use <b>Animations.ParentConstraint.GetTranslationOffset()</b>
          instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0062
        Title: UnityEngine.Animations.ParentConstraint.rotationOffsets
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Animations.ParentConstraint.rotationOffsets</b>
          allocates managed memory.
        Recommendation: Use <b>Animations.ParentConstraint.GetRotationOffset()</b>
          instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0063
        Title: UnityEngine.AnimationCurve.keys
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>AnimationCurve.keys</b> allocates managed memory.
        Recommendation: Use <b>AnimationCurve.AddKey()</b>, <b>AnimationCurve.MoveKey()</b>
          or <b>AnimationCurve.RemoveKey()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0066
        Title: UnityEngine.Camera.allCameras
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Camera.allCameras</b> allocates managed memory.
        Recommendation: Use <b>Camera.GetAllCameras()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0067
        Title: UnityEngine.Mesh.boneWeights
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.boneWeights</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetAllBoneWeights()</b> instead. This method
          returns a NativeArray of BoneWeight1, and so does not allocate managed
          memory.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0068
        Title: UnityEngine.Mesh.bindposes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.bindposes</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetBindposes()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0069
        Title: UnityEngine.Mesh.vertices
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.vertices</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetVertices()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0070
        Title: UnityEngine.Mesh.normals
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.normals</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetNormals()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0071
        Title: UnityEngine.Mesh.tangents
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.tangents</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetTangents()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0072
        Title: UnityEngine.Mesh.uv
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0073
        Title: UnityEngine.Mesh.uv1
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv1</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0074
        Title: UnityEngine.Mesh.uv2
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv2</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0075
        Title: UnityEngine.Mesh.uv3
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv3</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0076
        Title: UnityEngine.Mesh.uv4
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv4</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0077
        Title: UnityEngine.Mesh.uv5
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv5</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0078
        Title: UnityEngine.Mesh.uv6
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv6</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0079
        Title: UnityEngine.Mesh.uv7
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv7</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0080
        Title: UnityEngine.Mesh.uv8
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.uv8</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetUVs()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0081
        Title: UnityEngine.Mesh.colors
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.colors</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetColors()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0082
        Title: UnityEngine.Mesh.colors32
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.colors32</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetColors()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0083
        Title: UnityEngine.Mesh.triangles
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Mesh.triangles</b> allocates managed memory.
        Recommendation: Use <b>Mesh.GetTriangles()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0084
        Title: UnityEngine.Renderer.materials
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Renderer.materials</b> allocates managed memory.
        Recommendation: Use <b>Renderer.GetMaterials()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0085
        Title: UnityEngine.Renderer.sharedMaterials
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Renderer.sharedMaterials</b> allocates managed
          memory.
        Recommendation: Use <b>Renderer.GetSharedMaterials()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0094
        Title: UnityEngine.Input.touches
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Input.touches</b> allocates managed memory.
        Recommendation: Use <b>Input.GetTouch()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0095
        Title: UnityEngine.Input.accelerationEvents
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Input.accelerationEvents</b> allocates managed
          memory.
        Recommendation: Use <b>Input.GetAccelerationEvent()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0100
        Title: UnityEngine.GUISkin.customStyles
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>GUISkin.customStyles</b> allocates managed memory.
        Recommendation: Use <b>GUISkin.GetStyle()</b> or <b>GUISkin.FindStyle()</b>
          instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0103
        Title: UnityEngine.Collision.contacts
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Collision.contacts</b> allocates managed memory.
        Recommendation: Use <b>Collision.GetContacts()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0104
        Title: UnityEngine.Collision2D.contacts
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Collision2D.contacts</b> allocates managed memory.
        Recommendation: Use <b>Collision2D.GetContacts()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0110
        Title: UnityEngine.TerrainData.treeInstances
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TerrainData.treeInstances</b> allocates managed
          memory.
        Recommendation: Use <b>TerrainData.GetTreeInstance()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0111
        Title: UnityEngine.TerrainData.alphamapTextures
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TerrainData.alphamapTextures</b> allocates managed
          memory.
        Recommendation: Use <b>TerrainData.GetAlphamapTexture()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0112
        Title: UnityEngine.Font.characterInfo
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Font.characterInfo</b> allocates managed memory.
        Recommendation: Use <b>Font.GetCharacterInfo()</b> instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0115
        Title: UnityEngine.Animator.GetCurrentAnimatorClipInfo
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Animator.GetCurrentAnimatorClipInfo()</b> allocates managed
          memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0116
        Title: UnityEngine.Animator.GetBehaviours
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Animator.GetBehaviours()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0117
        Title: UnityEngine.AssetBundle.LoadAssetWithSubAssets
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>AssetBundle.LoadAssetWithSubAssets()</b> allocates managed
          memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0118
        Title: UnityEngine.AssetBundle.LoadAllAssets
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>AssetBundle.LoadAllAssets()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0119
        Title: UnityEngine.AssetBundleManifest.GetAllAssetBundles
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>AssetBundleManifest.GetAllAssetBundles()</b> allocates managed
          memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0120
        Title: UnityEngine.Resources.LoadAll
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Resources.LoadAll()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0121
        Title: UnityEngine.Texture2D.PackTextures
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Texture2D.PackTextures()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0122
        Title: UnityEngine.Cubemap.GetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Cubemap.GetPixels()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0123
        Title: UnityEngine.Texture3D.GetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Texture3D.GetPixels()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0124
        Title: UnityEngine.Texture3D.GetPixels32
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Texture3D.GetPixels32()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0125
        Title: UnityEngine.Texture2DArray.GetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Texture2DArray.GetPixels()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0126
        Title: UnityEngine.Texture2DArray.GetPixels32
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Texture2DArray.GetPixels32()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0127
        Title: UnityEngine.CubemapArray.GetPixels
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>CubemapArray.GetPixels()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0128
        Title: UnityEngine.CubemapArray.GetPixels32
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>CubemapArray.GetPixels32()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0129
        Title: UnityEngine.Object.FindObjectsOfType
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 68
        Platforms: 
        Description: <b>Object.FindObjectsOfType()</b> allocates managed memory and
          can be slow.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0130
        Title: UnityEngine.Windows.Crypto.ComputeMD5Hash
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Windows.Crypto.ComputeMD5Hash()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0131
        Title: UnityEngine.Windows.File.ReadAllBytes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Windows.File.ReadAllBytes()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0132
        Title: UnityEngine.ImageConversion.EncodeToJPG
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>ImageConversion.EncodeToJPG()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0133
        Title: UnityEngine.ImageConversion.EncodeToEXR
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>ImageConversion.EncodeToEXR()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0134
        Title: UnityEngine.ImageConversion.EncodeToTGA
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>ImageConversion.EncodeToTGA()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0135
        Title: UnityEngine.ImageConversion.EncodeToPNG
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>ImageConversion.EncodeToPNG()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0136
        Title: UnityEngine.U2D.SpriteShapeUtility.Generate
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>U2D.SpriteShapeUtility.Generate()</b> allocates managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0138
        Title: UnityEngine.AI.NavMeshTriangulation.layers
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>AI.NavMeshTriangulation.layers</b> allocates
          managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0139
        Title: UnityEngine.AnimationClip.events
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>AnimationClip.events</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0140
        Title: UnityEngine.AnimatorOverrideController.animationClips
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>AnimatorOverrideController.animationClips</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0141
        Title: UnityEngine.HumanTrait.MuscleName
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>HumanTrait.MuscleName</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0142
        Title: UnityEngine.HumanTrait.BoneName
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>HumanTrait.BoneName</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0143
        Title: UnityEngine.RuntimeAnimatorController.animationClips
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>RuntimeAnimatorController.animationClips</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0144
        Title: UnityEngine.AssetBundleRequest.allAssets
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>AssetBundleRequest.allAssets</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0145
        Title: UnityEngine.Microphone.devices
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Microphone.devices</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0146
        Title: UnityEngine.WebCamDevice.availableResolutions
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>WebCamDevice.availableResolutions</b> allocates
          managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0147
        Title: UnityEngine.WebCamTexture.devices
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>WebCamTexture.devices</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0148
        Title: UnityEngine.Cloth.vertices
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Cloth.vertices</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0149
        Title: UnityEngine.Cloth.normals
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Cloth.normals</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0150
        Title: UnityEngine.Cloth.coefficients
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Cloth.coefficients</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0151
        Title: UnityEngine.Cloth.capsuleColliders
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Cloth.capsuleColliders</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0152
        Title: UnityEngine.Cloth.sphereColliders
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Cloth.sphereColliders</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0153
        Title: UnityEngine.Camera.layerCullDistances
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Camera.layerCullDistances</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0154
        Title: UnityEngine.CrashReport.reports
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property CrashReport.reports allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0155
        Title: UnityEngine.Gradient.colorKeys
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Gradient.colorKeys</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0156
        Title: UnityEngine.Gradient.alphaKeys
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Gradient.alphaKeys</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0157
        Title: UnityEngine.Screen.resolutions
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Screen.resolutions</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0158
        Title: UnityEngine.LightmapSettings.lightmaps
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>LightmapSettings.lightmaps</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0159
        Title: UnityEngine.LightProbes.positions
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>LightProbes.positions</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0160
        Title: UnityEngine.LightProbes.bakedProbes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>LightProbes.bakedProbes</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0161
        Title: UnityEngine.LightProbes.coefficients
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>LightProbes.coefficients</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0162
        Title: UnityEngine.QualitySettings.names
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>QualitySettings.names</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0163
        Title: UnityEngine.Material.shaderKeywords
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Material.shaderKeywords</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0164
        Title: UnityEngine.Light.layerShadowCullDistances
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Light.layerShadowCullDistances</b> allocates
          managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0165
        Title: UnityEngine.Rendering.RenderTargetBinding.colorRenderTargets
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Rendering.RenderTargetBinding.colorRenderTargets</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0166
        Title: UnityEngine.Rendering.RenderTargetBinding.colorLoadActions
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Rendering.RenderTargetBinding.colorLoadActions</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0167
        Title: UnityEngine.Rendering.RenderTargetBinding.colorStoreActions
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Rendering.RenderTargetBinding.colorStoreActions</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0168
        Title: UnityEngine.SkinnedMeshRenderer.bones
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>SkinnedMeshRenderer.bones</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0169
        Title: UnityEngine.LightProbeGroup.probePositions
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>LightProbeGroup.probePositions</b> allocates
          managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0172
        Title: UnityEngine.SortingLayer.layers
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>SortingLayer.layers</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0173
        Title: UnityEngine.TextAsset.bytes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TextAsset.bytes</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0177
        Title: UnityEngine.Sprite.vertices
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Sprite.vertices</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0178
        Title: UnityEngine.Sprite.triangles
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Sprite.triangles</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0179
        Title: UnityEngine.Sprite.uv
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Sprite.uv</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0180
        Title: UnityEngine.SocialPlatforms.ILocalUser.friends
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>SocialPlatforms.ILocalUser.friends</b> allocates
          managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0181
        Title: UnityEngine.SocialPlatforms.ILeaderboard.scores
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>SocialPlatforms.ILeaderboard.scores</b> allocates
          managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0182
        Title: UnityEngine.GUIStyleState.scaledBackgrounds
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>GUIStyleState.scaledBackgrounds</b> allocates
          managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0183
        Title: UnityEngine.EdgeCollider2D.points
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>EdgeCollider2D.points</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0184
        Title: UnityEngine.PolygonCollider2D.points
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>PolygonCollider2D.points</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0185
        Title: UnityEngine.Terrain.activeTerrains
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Terrain.activeTerrains</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0186
        Title: UnityEngine.TerrainData.detailPrototypes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TerrainData.detailPrototypes</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0187
        Title: UnityEngine.TerrainData.treePrototypes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TerrainData.treePrototypes</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0188
        Title: UnityEngine.TerrainData.splatPrototypes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TerrainData.splatPrototypes</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0189
        Title: UnityEngine.TerrainData.terrainLayers
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TerrainData.terrainLayers</b> allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0190
        Title: UnityEngine.Tilemaps.TileAnimationData.animatedSprites
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Tilemaps.TileAnimationData.animatedSprites</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0191
        Title: UnityEngine.UIElements.UxmlAttributeDescription.obsoleteNames
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>UIElements.UxmlAttributeDescription.obsoleteNames</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0200
        Title: UnityEngine.Networking.IMultipartFormSection.sectionData
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Networking.IMultipartFormSection.sectionData</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0201
        Title: UnityEngine.Networking.MultipartFormDataSection.sectionData
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Networking.MultipartFormDataSection.sectionData</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0202
        Title: UnityEngine.Networking.MultipartFormFileSection.sectionData
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Networking.MultipartFormFileSection.sectionData</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0203
        Title: UnityEngine.WWWForm.data
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>WWWForm.data</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0204
        Title: UnityEngine.Networking.DownloadHandler.data
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property Networking.DownloadHandler.data allocates managed
          memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0208
        Title: UnityEngine.Networking.UploadHandler.data
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>Networking.UploadHandler.data</b> allocates
          managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0217
        Title: UnityEngine.WWW.bytes
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>WWW.bytes</b> allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0219
        Title: UnityEngine.XR.XRSettings.supportedDevices
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>XR.XRSettings.supportedDevices</b> allocates
          managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0220
        Title: UnityEngine.TestTools.Constraints.AllocatingGCMemoryConstraint.Arguments
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TestTools.Constraints.AllocatingGCMemoryConstraint.Arguments</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0221
        Title: UnityEngine.TestTools.UnityPlatformAttribute.include
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TestTools.UnityPlatformAttribute.include</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0222
        Title: UnityEngine.TestTools.UnityPlatformAttribute.exclude
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The property <b>TestTools.UnityPlatformAttribute.exclude</b>
          allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0223
        Title: UnityEngine.GameObject.tag
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The <b>GameObject.tag</b> property allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Prefer using <b>GameObject.CompareTag()</b> instead, as this does
          not result in managed allocations.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0224
        Title: UnityEngine.Object.Instantiate
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 68
        Platforms: 
        Description: Creating Objects at runtime by calling <b>Object.Instantiate</b>
          can take a significant amount of CPU time and allocates managed memory.
        Recommendation: Try to avoid calling <b>Object.Instantiate</b> in frequently-updated
          code. Consider implementing an Object Pool. The <b>ObjectPool</b> class
          is available from Unity 2021.1 as a convenience.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0225
        Title: UnityEngine.GameObject.AddComponent
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 68
        Platforms: 
        Description: Adding components to GameObjects at runtime can take a significant
          amount of CPU time and allocates managed memory.
        Recommendation: Try to avoid adding or removing components in frequently-updated
          code. Prefer instantiating GameObjects from Prefabs will all the necessary
          components instead.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0227
        Title: UnityEngine.Shader.WarmupAllShaders
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>WarmupAllShaders</b> does not work properly on Metal, Vulkan
          or DX12. This might result in unexpected CPU spikes due to shader compilation.
        Recommendation: Implement a shader pre-warming mechanism which renders a
          small triangle for each combination of vertex format and shader used at
          runtime. One way to achieve this is with the <b>UnityEngine.Experimental.Rendering.ShaderWarmup</b>
          API.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0228
        Title: UnityEngine.ShaderVariantCollection.WarmUp
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>WarmUp</b> does not work properly on Metal, Vulkan or DX12.
          This might result in unexpected CPU spikes due to shader compilation.
        Recommendation: Implement a shader pre-warming mechanism which renders a
          small triangle for each combination of vertex format and shader variant
          used at runtime. One way to achieve this is with the <b>UnityEngine.Experimental.Rendering.ShaderWarmup</b>
          API.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0229
        Title: UnityEngine.Component.tag
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The <b>Component.tag</b> property allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Prefer using <b>CompareTag()</b> instead, as this does not result
          in managed allocations.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0231
        Title: UnityEngine.Object.name
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The <b>Object.name</b> property allocates managed memory.
        Recommendation: Try to avoid getting this property in frequently-updated
          code. Ideally, this property should only be used during initialisation,
          and the results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC1000
        Title: System.Linq.*
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 68
        Platforms: 
        Description: Linq allocates large amounts of managed memory and exhibits
          poor CPU performance.
        Recommendation: We strongly advise against using Linq in any frequently-updated
          code. Ban its usage from the project entirely, or confine it to initialization
          code and use it sparingly.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC1001
        Title: System.Reflection.*
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: Reflection is slow, and not generally considered performant
          enough for runtime code.
        Recommendation: Remove code which relies on reflection, or minimise its usage,
          particularly outside of initialization.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC1002
        Title: System.String.Concat
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: String concatenation operations allocates managed memory.
        Recommendation: Try to avoid concatenating strings in frequently-updated
          code. Prefer using a StringBuilder instead, as this minimizes managed allocations.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC1003
        Title: System.DateTime.Now
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>System.DateTime.Now</b> can take a lot of CPU time because
          it needs to figure out the current timezone and daylight saving time information.
        Recommendation: Try to avoid using this method in frequently-updated code.
          Prefer UnityEngine.Time.time or, if precise time is needed, use DateTime.UtcNow.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC1004
        Title: System.AppDomain.GetAssemblies
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>System.AppDomain.GetAssemblies</b> can take a lot of CPU
          time.
        Recommendation: Try to minimize calls to <b>System.AppDomain.GetAssemblies</b>
          by caching the returned assemblies. When possible, use <b>UnityEditor.TypeCache</b>
          for fast access to types, methods and fields.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0232
        Title: UnityEditor.AssetDatabase.FindAssets
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>UnityEditor.AssetDatabase.FindAssets</b> is a CPU-intensive
          operation and can slow down the Editor for long periods of time on large
          projects.
        Recommendation: Try to minimize calls to <b>UnityEditor.AssetDatabase.FindAssets</b>.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC0234
        Title: UnityEngine.Object.FindObjectOfType
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 68
        Platforms: 
        Description: <b>Object.FindObjectOfType()</b> allocates managed memory and
          can be slow.
        Recommendation: Try to avoid calling this method in frequently-updated code.
          Ideally, this method should only be used during initialisation, and the
          results should be cached if they need to be re-used.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC2001
        Title: Empty MonoBehaviour Method
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: Any empty MonoBehaviour message handling method (for example,
          Awake(), Start(), Update()) will be included in the build and executed
          even if it is empty. Every message handling method on every instance of
          a MonoBehaviour takes a small amount of CPU time.
        Recommendation: Remove any empty MonoBehaviour methods.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC2002
        Title: Object Allocation
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: An object is allocated in managed memory.
        Recommendation: Try to avoid allocating objects in frequently-updated code.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC2003
        Title: Closure Allocation
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: A closure is allocating managed memory. A closure occurs when
          a variable's state is captured by an in-line delegate, anonymous method
          or lambda which accesses that variable.
        Recommendation: Try to avoid allocating objects in frequently-updated code.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC2004
        Title: Array Allocation
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: An array is allocated in managed memory.
        Recommendation: Try to avoid allocating arrays in frequently-updated code.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAC2005
        Title: Param Object Allocation
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: A parameters array is allocated in managed memory.
        Recommendation: Try to avoid calling this method in frequently-updated code.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1001
        Title: 'HDRP: Render Pipeline Assets use both Lit Shader Modes'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 3
        Platforms: 
        Description: The <b>Lit Shader Mode</b> option in the HDRP Asset is set to
          <b>Both</b>. As a result, shaders will be built for both Forward and Deferred
          rendering. This increases build time and size.
        Recommendation: Change <b>Lit Shader Mode</b> to either <b>Forward</b> or
          <b>Deferred</b>.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1002
        Title: 'HDRP: Cameras mix usage of Lit Shader Modes'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 3
        Platforms: 
        Description: Project contains Multiple HD Cameras, some of which have <b>Lit
          Shader Mode</b> set to <b>Forward</b>, and some to <b>Deferred</b>. As
          a result, shaders will be built for both Forward and Deferred rendering.
          This increases build time and size.
        Recommendation: Change the <b>Lit Shader Mode</b> in all HDRP Assets and
          all Cameras to either <b>Forward</b> or <b>Deferred</b>.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1003
        Title: 'Graphics: Fog Mode is enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 1
        Platforms: 
        Description: <b>Fog Modes</b> in Graphics Settings are set to build all fog
          shader variants for this fog mode. Forcing Fog shader variants to be built
          can increase the build size.
        Recommendation: Change <b>Project Settings > Graphics > Fog Modes</b> to
          <b>Automatic</b> or disable <b>Linear/Exponential/Exponential Squared</b>.
          This should reduce the number of shader variants generated for fog effects.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0016
        Title: 'Time: Fixed Timestep is set to the default value'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: In the Time Settings, <b>Fixed Timestep</b> is set to the default
          value of <b>0.02</b>. This means that Unity will try to ensure that the
          FixedUpdate() methods of MonoBehaviours, and physics updates will be called
          50 times per second. This is appropriate for games running at 60 FPS, but
          at 30 FPS this would mean that the FixedUpdate step will be called twice
          during most frames.
        Recommendation: We recommend setting <b>Fixed Timestep</b> to 0.04 when running
          at 30 FPS, in order to call the fixed updates at 25 Hz. The reason for
          having the fixed update be slightly less than the target frame rate is
          to avoid the "spiral of death", in which if one frame takes longer than
          33.3ms, FixedUpdate() happens multiple times on the next frame to catch
          up, pushing that frame time over as well, and permanently locking the game
          into a state where it cannot reach the desired frame rate because FixedUpdate()
          is constantly trying to catch up.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0017
        Title: 'Time: Maximum Allowed Timestep is set to the default value'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: In the Time Settings, <b>Maximum Allowed Timestep</b> is set
          to the default value of <b>0.1</b>. This means that if the Time Manager
          is trying to "catch" up with previous frames that took longer than <b>Fixed
          Timestep</b> to process, the project's FixedUpdate() methods could end
          up being called repeatedly, up to a maximum of 0.1 seconds (100 milliseconds).
          Spending so long in FixedUpdate() would likely mean that FixedUpdate()
          must also be called multiple times in the subsequent frames, contributing
          to the "spiral of death".
        Recommendation: Consider reducing <b>Maximum Allowed Timestep</b> to a time
          that can be comfortably accommodated within your project's target frame
          rate.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0022
        Title: 'Graphics: Shader Quality uses a mixture of different values'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 1
        Platforms: 
        Description: The current build target Graphics Tier Settings use a mixture
          of different values (Low/Medium/High) for the <b>Standard Shader Quality</b>
          setting. This will result in a larger number of shader variants being compiled,
          which will increase build times and your application's download/install
          size.
        Recommendation: Unless you support devices with a very wide range of capabilities
          for a particular platform, consider editing the platform in Graphics Settings
          to use the same shader quality setting across all Graphics Tiers.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0023
        Title: 'Graphics: Rendering Path is set to Forward Rendering'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 8
        Platforms: 
        Description: The current build target uses forward rendering, as set in the
          <b>Rendering Path</b> settings in <b>Project Settings > Graphics > Tier
          Settings</b>. This can impact GPU performance in projects with nontrivial
          numbers of dynamic lights.
        Recommendation: This rendering path is suitable for games with simple rendering
          and lighting requirements - for instance, 2D games, or games which mainly
          use baked lighting. If the project makes use of a more than a few dynamic
          lights, consider experimenting with changing <b>Rendering Path</b> to Deferred
          to see whether doing so improves GPU rendering times.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0024
        Title: 'Graphics: Rendering Path is set to Deferred Rendering'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 8
        Platforms: 
        Description: The current build target uses deferred rendering, as set in
          the <b>Rendering Path</b> settings in <b>Project Settings > Graphics >
          Tier Settings</b>. This can impact GPU performance in projects with simple
          rendering requirements.
        Recommendation: This rendering path is suitable for games with more complex
          rendering requirements - for instance, games that make uses of dynamic
          lighting or certain types of fullscreen post-processing effects. If the
          project doesn't make use of such rendering techniques, consider experimenting
          with changing <b>Rendering Path</b> to Forward to see whether doing so
          improves GPU rendering times.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0015
        Title: 'Physics2D: Layer Collision Matrix has all boxes ticked'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: In Physics2D Settings, all of the boxes in the <b>Layer Collision
          Matrix</b> are ticked. This increases the CPU work required to calculate
          collision detections.
        Recommendation: Un-tick all of the boxes except the ones that represent collisions
          that should be considered by the 2D physics system.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0032
        Title: 'Physics2D: Simulation Mode is set to automatically update'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Simulation Mode</b> in Physics2D Settings is set to either
          <b>FixedUpdate</b> or <b>Update</b>. As a result, 2D physics simulation
          is executed on every update which might be expensive for some projects.
        Recommendation: Change <b>Project Settings > Physics 2D > Simulation Mode</b>
          to <b>Script</b> to disable the 2d physics processing each frame. If physics
          simulation is required for certain special rendering, use <b>Script</b>
          mode to control <b>Physics2d.Simulate</b> on a per frame basis.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0018
        Title: 'Quality: Using default Quality Levels'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 45
        Platforms: 
        Description: This project is using the default set of <b>Quality Levels</b>
          defined in Quality Settings. This can make it difficult to understand the
          range of rendering settings used in the project, and can result in an unnecessarily
          large number of shader variants, impacting build times and runtime memory
          usage.
        Recommendation: Check the quality setting for each platform the project supports
          in the grid - it's the level with the green tick. Remove quality levels
          you are not using, to make the Quality Settings simpler to see and edit.
          Adjust the setting for each platform if necessary, then select the appropriate
          levels to examine their settings in the panel below.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0019
        Title: 'Quality: Texture Quality is not set to Full Res'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 9
        Platforms: 
        Description: One or more of the <b>Quality Levels</b> in the project's Quality
          Settings has <b>Texture Quality</b> set to something other than <b>Full
          Res</b>. This option can save memory on lower-spec devices and platforms
          by discarding higher-resolution mip levels on mipmapped textures before
          uploading them to the GPU. However, this option has no effect on textures
          which don't have mipmaps enabled (as is frequently the case with UI textures,
          for instance), does nothing to reduce download or install size, and gives
          you no control over the texture resize algorithm.
        Recommendation: For devices which must use lower-resolution versions of textures,
          consider creating these lower resolution textures separately, and choosing
          the appropriate content to load at runtime using AssetBundle variants.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0020
        Title: 'Quality: Async Upload Time Slice is set to default value'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 32
        Platforms: 
        Description: The <b>Async Upload Time Slice</b> option for one or more <b>Quality
          Levels</b> in the project's Quality Settings is set to the default value
          of <b>2ms</b>.
        Recommendation: If the project encounters long loading times when loading
          large amount of texture and/or mesh data, experiment with increasing this
          value to see if it allows content to be uploaded to the GPU more quickly.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0021
        Title: 'Quality: Async Upload Buffer Size is set to default value'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 32
        Platforms: 
        Description: The <b>Async Upload Buffer Size</b> option for one or more <b>Quality
          Levels</b> in the project's Quality Settings is set to the default value.
        Recommendation: If the project encounters long loading times when loading
          large amount of texture and/or mesh data, experiment with increasing this
          value to see if it allows content to be uploaded to the GPU more quickly.
          This is most likely to help if you are loading large textures. Note that
          this setting controls a buffer size in megabytes, so exercise caution if
          memory is limited in your application.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1007
        Title: 'Quality: Mipmap streaming is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: <b>Mipmap Streaming</b> is disabled in Quality Settings. As
          a result, all mip levels for all loaded textures are loaded into GPU memory,
          potentially resulting in excessive texture memory usage.
        Recommendation: If your project contains many high resolution mipmapped textures,
          enable <b>Mipmap Streaming</b> in Quality Settings.
        DocumentationUrl: https://docs.unity3d.com/Manual/TextureStreaming.html
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1009
        Title: 'URP: URP Asset is not specified'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 136
        Platforms: 
        Description: Graphics Settings do not refer to a URP Asset.
        Recommendation: 'Check the settings: Graphics > Scriptable Render Pipeline
          Settings > Render Pipeline Asset.'
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0001
        Title: 'Player: Graphics Jobs is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: The <b>Graphics Jobs</b> option in Player Settings is disabled.
          This may introduce CPU rendering performance bottlenecks.
        Recommendation: 'Try enabling <b>Graphics Jobs</b> and testing your application.
          This option spreads the task of building the render command buffer every
          frame across as many CPU cores as possible, rather than performing all
          the work in the render thread which is often a bottleneck. Performance
          will vary depending on the project.


          Note: This feature is experimental
          on specific Unity versions and may introduce new crashes. It is recommended
          to test accordingly.'
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0007
        Title: 'Player: Prebake Collision Meshes is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 33
        Platforms: 
        Description: The <b>Prebake Collision Meshes</b> option in Player Settings
          is disabled. This may result in longer application load times, although
          enabling the option may increase build times and sizes.
        Recommendation: If you are using physics in your application, consider enabling
          <b>Prebake Collision Meshes</b>, at least before creating release and profiling
          builds. Prebaked collision meshes can result in an increase in build times
          and sizes, but reduce loading/initialization times in your application,
          because serializing prebaked meshes is faster than baking them at runtime.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0008
        Title: 'Player: Optimize Mesh Data is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 41
        Platforms: 
        Description: The <b>Optimize Mesh Data</b> option in Player Settings is disabled.
          Your project may be building and loading unused vertex channel information.
        Recommendation: Consider enabling <b>Optimize Mesh Data</b>. This option
          strips out vertex channels on meshes which are not used by the materials
          which are applied to them. This can reduce the file size of your meshes
          and the time to load them, and increase GPU rendering performance. It can,
          however, cause problems if mesh materials are changed at runtime, since
          the new materials might rely on vertex channels which have been removed,
          and it may contribute to longer build times.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0012
        Title: 'Physics: Auto Sync Transforms is enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: In Physics Settings, <b>Auto Sync Transforms</b> is enabled.
          This option ensures backwards compatibility with the behaviour of older
          versions of Unity in which physics transforms were always kept in sync
          with GameObject transforms. In newer versions of Unity, transform syncs
          are batched for greater efficiency on the CPU. Enabling this option means
          that transforms are always synced before physics queries (e.g. Physics.Raycast());
          before reading data back from the physics engine (e.g. Rigidbody.position);
          before simulating particles that compute collisions, and before updating
          the camera flares effect. This adds an additional CPU cost.
        Recommendation: Consider disabling <b>Auto Sync Transforms</b> and testing
          your game to identify any areas where physics behavior is affected by the
          change. If there are areas of the game where more frequent synchronization
          is required to maintain the desired behaviour, this can be enforced by
          calling Physics.SyncTransforms() directly.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0014
        Title: 'Physics2D: Auto Sync Transforms is enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: In Physics 2D Settings, <b>Auto Sync Transforms</b> is enabled.
          This option ensures backwards compatibility with the behaviour of older
          versions of Unity in which physics transforms were always kept in sync
          with GameObject transforms. In newer versions of Unity, transform syncs
          are batched for greater efficiency on the CPU. Enabling this option means
          that transforms are always synced before physics queries (e.g. Physics2D.Raycast());
          before reading data back from the physics engine (e.g. Rigidbody2D.position);
          before simulating particles that compute collisions, and before updating
          the camera flares effect. This adds an additional CPU cost.
        Recommendation: Consider disabling <b>Auto Sync Transforms</b> and testing
          your game to identify any areas where physics behavior is affected by the
          change. If there are areas of the game where more frequent synchronization
          is required to maintain the desired behaviour, this can be enforced by
          calling Physics2D.SyncTransforms() directly.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0027
        Title: 'Player: Mipmap Stripping is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 1
        Platforms: 
        Description: The <b>Texture MipMap Stripping</b> option in Player Settings
          is disabled. As a result, the generated build might be larger than necessary.
        Recommendation: Enable <b>Texture MipMap Stripping</b>. Note that this feature
          will only reduce the build size if no quality levels on the platform use
          highest mip(s). Furthermore, if code drives the <b>masterTextureLevel</b>
          to a value higher than those in the quality level settings the mip will
          no longer be available if this is enabled.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0028
        Title: 'Physics: Reuse Collision Callbacks is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The <b>Reuse Collision Callbacks</b> option in Physics Settings
          is disabled. For each OnCollision* callback, a temporary managed object
          is allocated.
        Recommendation: When this option is enabled, only a single instance of the
          Collision type is created and reused for each individual callback. This
          reduces waste for the garbage collector to handle and improves performance.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0034
        Title: 'Player: Use incremental GC is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: The <b>Incremental Garbage Collection</b> feature is disabled.
          This might lead to CPU spikes due to Garbage Collection.
        Recommendation: To enable this feature, enable option <b>Project Settings
          > Player > Other Settings > Configuration > Use incremental GC</b>. Note
          this is not a substitute for reducing per-frame managed allocations.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0029
        Title: 'Player: Splash Screen is enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 32
        Platforms: 
        Description: <b>Show Splash Screen</b> is enabled in the Player Settings.
          Displaying a splash screen will increase the time it takes to load into
          the first scene.
        Recommendation: Disable the Splash Screen option in <b>Project Settings >
          Player > Splash Image > Show Splash Screen</b>.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1004
        Title: 'Player: IL2CPP Compiler Configuration is set to Master'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 2
        Platforms: 
        Description: <b>C++ Compiler Configuration</b> in Player Settings is set
          to <b>Master</b>. This mode is intended for shipping builds and will significantly
          increase build times.
        Recommendation: Change <b>Project Settings > Player > Other Settings > Configuration
          > C++ Compiler Configuration</b> to <b>Release</b>.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1005
        Title: 'Player: IL2CPP Compiler Configuration is set to Debug'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>C++ Compiler Configuration</b> is set to <b>Debug</b>. This
          mode is intended for debugging and might have an impact on runtime CPU
          performance.
        Recommendation: Change <b>Project Settings > Player > Other Settings > Configuration
          > C++ Compiler Configuration</b> to <b>Release</b>.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1006
        Title: 'Player: Lightmap Streaming is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 12
        Platforms: 
        Description: <b>Lightmap Streaming</b> in Player Settings is not enabled.
          As a result, all lightmap detail levels are loaded into GPU memory, potentially
          resulting in excessive lightmap texture memory usage.
        Recommendation: Enable <b>Lightmap Streaming</b> in <b>PProject Settings
          > Player > Other Settings > Rendering</b>.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1000
        Title: 'Player Settings: Static batching is enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Static Batching</b> is enabled in Player Settings and the
          package com.unity.rendering.hybrid is installed. Static batching is incompatible
          with the batching techniques used in the Hybrid Renderer and Scriptable
          Render Pipeline, and will result in poor rendering performance and excessive
          memory use.
        Recommendation: Disable static batching in Player Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1013
        Title: 'Player Settings: Static batching is enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>Static Batching</b> is enabled in Player Settings and the
          package com.unity.entities.graphics is installed. Static batching is incompatible
          with the batching techniques used in Entities Graphics and the Scriptable
          Render Pipeline, and will result in poor rendering performance and excessive
          memory use.
        Recommendation: Disable static batching in Player Settings.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS1008
        Title: 'SRP Asset: SRP Batcher is disabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: <b>SRP Batcher</b> is disabled in a Render Pipeline Asset.
        Recommendation: Enable <b>SRP Batcher</b> in Render Pipeline Asset. If the
          option is hidden, click the vertical ellipsis icon and select <b>Show Additional
          Properties</b>. Enabling the SRP Batcher will reduce the CPU time Unity
          requires to prepare and dispatch draw calls for materials that use the
          same shader variant.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAS0013
        Title: 'Physics: Layer Collision Matrix has all boxes ticked'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 4
        Platforms: 
        Description: In Physics Settings, all of the boxes in the <b>Layer Collision
          Matrix</b> are ticked. This increases the CPU work required to calculate
          collision detections.
        Recommendation: Un-tick all of the boxes except the ones that represent collisions
          that should be considered by the Physics system.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAP0001
        Title: Newer recommended package version
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 128
        Platforms: 
        Description: A newer recommended version of this package is available.
        Recommendation: Update the package via Package Manager.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAP0002
        Title: Experimental/Preview packages
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 128
        Platforms: 
        Description: Experimental or Preview packages are in the early stages of
          development and not yet ready for production.
        Recommendation: Experimental packages should only be used for testing purposes
          and to give feedback to Unity.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA1000
        Title: 'Mesh: Read/Write enabled'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The <b>Read/Write Enabled</b> flag in the Model Import Settings
          is enabled. This causes the mesh data to be duplicated in memory.
        Recommendation: If not required, disable the <b>Read/Write Enabled</b> option
          in the Model Import Settings.
        DocumentationUrl: https://docs.unity3d.com/Manual/FBXImporter-Model.html
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA1001
        Title: 'Mesh: Index Format is 32 bits'
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 64
        Platforms: 
        Description: The <b>Index Format</b> in the Model Import Settings is set
          to <b>32 bit</b>. This increases the mesh size and may not work on certain
          mobile devices.
        Recommendation: Consider using changing the <b>Index Format</b> option in
          the Model Import Settings. This should be set to either <b>16 bits</b>
          or <b>Auto</b>.
        DocumentationUrl: https://docs.unity3d.com/Manual/FBXImporter-Model.html
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA3000
        Title: Resources folder asset
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 1
        Platforms: 
        Description: "The <b>Resources folder</b> is a common source of many problems
          in Unity projects. Improper use of the Resources folder can bloat the size
          of a project\u2019s build, lead to uncontrollable excessive memory utilization,
          and significantly increase application startup times."
        Recommendation: Use AssetBundles or Addressables when possible.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
      - Id: PAA3001
        Title: Resources folder asset dependency
        MessageFormat: 
        DefaultSeverity: 4
        IsEnabledByDefault: 1
        Areas: 1
        Platforms: 
        Description: "The <b>Resources folder</b> is a common source of many problems
          in Unity projects. Improper use of the Resources folder can bloat the size
          of a project\u2019s build, lead to uncontrollable excessive memory utilization,
          and significantly increase application startup times."
        Recommendation: Use AssetBundles or Addressables when possible.
        DocumentationUrl: 
        MinimumVersion: 
        MaximumVersion: 
        Type: 
        Method: 
        Value: 
    m_Issues: []
  m_AnalysisState: 1
  m_ViewStates:
    info: 1
    info2: 1
    filters: 1
    dependencies: 1
    onlyCriticalIssues: 0
    fontSize: 12
  m_ViewManager:
    m_Categories: 000000000400000001000000020000000600000013000000030000000c0000000e000000110000001400000015000000160000001700000012000000180000000b0000000a0000000500000008000000070000000d0000000f000000
    m_ActiveViewIndex: 0
  m_Tabs:
  - id: 0
    name: Summary
    categories: 00000000
    currentCategoryIndex: 0
  - id: 1
    name: Code
    categories: 040000000a0000000b0000000500000018000000
    currentCategoryIndex: 0
  - id: 2
    name: Assets
    categories: 010000000e00000012000000110000000f00000014000000150000001600000017000000
    currentCategoryIndex: 0
  - id: 3
    name: Shaders
    categories: 02000000030000000c00000013000000
    currentCategoryIndex: 0
  - id: 4
    name: Project
    categories: 060000000d000000
    currentCategoryIndex: 0
  - id: 5
    name: Build
    categories: 0700000008000000
    currentCategoryIndex: 0
  m_ActiveTabIndex: 0
  m_ViewSelectionTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 01000000
    m_LastClickedID: 1
    m_ExpandedIDs: 
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
  m_IsNonAnalyzedViewSelected: 0
  m_SelectedNonAnalyzedTab:
    id: 0
    name: 
    categories: 
    currentCategoryIndex: 0
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12070, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 900, y: 216}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Profiler
    m_Image: {fileID: -1089619856830078684, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Profiler\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2290
    y: 82
    width: 900
    height: 508
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Recording: 1
  m_ActiveNativePlatformSupportModuleName: 
  m_AllModules:
  - rid: 1171111306876682739
  - rid: 1171111306876682740
  - rid: 1171111306876682741
  - rid: 1171111306876682742
  - rid: 1171111306876682743
  - rid: 1171111306876682744
  - rid: 1171111306876682745
  - rid: 1171111306876682746
  - rid: 1171111306876682747
  - rid: 1171111306876682748
  - rid: 1171111306876682749
  - rid: 1171111306876682750
  - rid: 1171111306876682751
  - rid: 1171111306876682752
  - rid: 1171111306876682753
  - rid: 1171111306876682754
  - rid: 1171111306876682755
  m_CallstackRecordMode: 1
  m_ClearOnPlay: 0
  references:
    version: 2
    RefIds:
    - rid: 1171111306876682739
      type: {class: CPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.CPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 1
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns:
            - width: 200
              sortedAscending: 1
              headerContent:
                m_Text: Overview
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Overview\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 200
              maxWidth: 1000000
              autoResize: 1
              allowToggleVisibility: 0
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Total
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Total\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Calls
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Calls\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: GC Alloc
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "GC Alloc\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Time ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Time ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 25
              sortedAscending: 0
              headerContent:
                m_Text: 
                m_Image: {fileID: -5161429177145976760, guid: 0000000000000000d000000000000000, type: 0}
                m_Tooltip: Warnings
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 25
              maxWidth: 25
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            m_VisibleColumns: 0000000001000000020000000300000004000000050000000600000007000000
            m_SortedColumns: 05000000
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: -1
            realSizes:
            - 0
            - 0
            relativeSizes:
            - 0.7
            - 0.3
            minSizes:
            - 450
            - 50
            maxSizes:
            - 0
            - 0
            lastTotalSize: 0
            splitSize: 6
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: -1
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns: []
                m_VisibleColumns: 
                m_SortedColumns: 
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns: []
                m_VisibleColumns: 
                m_SortedColumns: 
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 1171111306876682740
      type: {class: GPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 0
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns: []
            m_VisibleColumns: 
            m_SortedColumns: 
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: 0
            realSizes: []
            relativeSizes: []
            minSizes: []
            maxSizes: []
            lastTotalSize: 0
            splitSize: 0
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 0
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: 0
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns: []
                m_VisibleColumns: 
                m_SortedColumns: 
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns: []
                m_VisibleColumns: 
                m_SortedColumns: 
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 1171111306876682741
      type: {class: RenderingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.RenderingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682742
      type: {class: MemoryProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.MemoryProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewSplit:
          ID: 0
          splitterInitialOffset: 0
          currentActiveSplitter: -1
          realSizes:
          - 0
          - 0
          relativeSizes:
          - 0.7
          - 0.3
          minSizes:
          - 450
          - 50
          maxSizes:
          - 0
          - 0
          lastTotalSize: 0
          splitSize: 6
          xOffset: 0
          m_Version: 1
          oldRealSizes: 
          oldMinSizes: 
          oldMaxSizes: 
          oldSplitSize: 0
    - rid: 1171111306876682743
      type: {class: AudioProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AudioProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ShowInactiveDSPChains: 0
        m_HighlightAudibleDSPChains: 1
        m_DSPGraphZoomFactor: 1
        m_DSPGraphHorizontalLayout: 0
    - rid: 1171111306876682744
      type: {class: VideoProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VideoProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682745
      type: {class: PhysicsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.PhysicsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682746
      type: {class: Physics2DProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.Physics2DProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682747
      type: {class: UIProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682748
      type: {class: UIDetailsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIDetailsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682749
      type: {class: GlobalIlluminationProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GlobalIlluminationProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682750
      type: {class: VirtualTexturingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VirtualTexturingProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_VTProfilerView:
          rid: 1171111306876682756
    - rid: 1171111306876682751
      type: {class: MemoryProfilerModule, ns: Unity.Entities.Editor, asm: Unity.Entities.Editor}
      data:
        m_Identifier: Unity.Entities.Editor.MemoryProfilerModule, Unity.Entities.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    - rid: 1171111306876682752
      type: {class: FileIOProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.FileIOProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682753
      type: {class: AssetLoadingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AssetLoadingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 1171111306876682754
      type: {class: StructuralChangesProfilerModule, ns: Unity.Entities.Editor, asm: Unity.Entities.Editor}
      data:
        m_Identifier: Unity.Entities.Editor.StructuralChangesProfilerModule, Unity.Entities.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    - rid: 1171111306876682755
      type: {class: RuntimeContentManagerProfilerModule, ns: , asm: Unity.Entities.Editor}
      data:
        m_Identifier: RuntimeContentManagerProfilerModule, Unity.Entities.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    - rid: 1171111306876682756
      type: {class: VirtualTexturingProfilerView, ns: UnityEditor, asm: UnityEditor.CoreModule}
      data:
        m_SortAscending: 0
        m_SortedColumn: -1
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: -6423792434712278376, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Game\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 26
    width: 1920
    height: 983
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SerializedViewNames:
  - UnityEditor.DeviceSimulation.SimulatorWindow
  m_SerializedViewValues:
  - J:\BTR U6 2025 Render Graph\Library\PlayModeViewStates\f99499ff2c96f6a4d8917e689ca8ea60
  m_PlayModeViewName: GameView
  m_ShowGizmos: 0
  m_TargetDisplay: 0
  m_ClearColor: {r: 0, g: 0, b: 0, a: 0}
  m_TargetSize: {x: 1920, y: 1080}
  m_TextureFilterMode: 0
  m_TextureHideFlags: 61
  m_RenderIMGUI: 1
  m_EnterPlayModeBehavior: 2
  m_UseMipMap: 0
  m_VSyncEnabled: 0
  m_Gizmos: 0
  m_Stats: 0
  m_SelectedSizes: 03000000000000000000000000000000000000000000000000000000000000000000000000000000
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    hZoomLockedByDefault: 0
    vZoomLockedByDefault: 0
    m_HBaseRangeMin: -960
    m_HBaseRangeMax: 960
    m_VBaseRangeMin: -540
    m_VBaseRangeMax: 540
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 1
    m_EnableSliderZoomHorizontal: 0
    m_EnableSliderZoomVertical: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 21
      width: 1920
      height: 962
    m_Scale: {x: 0.89074075, y: 0.89074075}
    m_Translation: {x: 960, y: 481}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -1077.7546
      y: -540
      width: 2155.5093
      height: 1080
    m_MinimalGUI: 1
  m_defaultScale: 0.89074075
  m_LastWindowPixelSize: {x: 1920, y: 983}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 01000000000000000000
  m_XRRenderMode: 0
  m_RenderTexture: {fileID: 0}
  m_showToolbar: 1
--- !u!114 &18
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: 7966133145522015247, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Hierarchy\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 24
    width: 369
    height: 508
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SceneHierarchy:
    m_TreeViewState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: b8faffff
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_TrimLeadingAndTrailingWhitespace: 0
        m_ClientGUIView: {fileID: 11}
      m_SearchString: 
    m_ExpandedScenes: []
    m_CurrenRootInstanceID: 0
    m_LockTracker:
      m_IsLocked: 0
    m_CurrentSortingName: TransformSorting
  m_WindowGUID: 4c969a2b90040154d917609493e03593
--- !u!114 &19
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 2593428753322112591, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Scene\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2290
    y: 82
    width: 900
    height: 508
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: Tool Settings
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-169.0,"y":-26.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -169, y: -26}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-grid-and-snap-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-141.0,"y":-200.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -141, y: -200}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-toolbar
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 0
      id: unity-search-toolbar
      index: 2
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":-24.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: -24, y: 0}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-transform-toolbar
      index: 0
      contents: '{"m_Layout":2,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":-278.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":2,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: -278}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 2
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-component-tools
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 197}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: Orientation
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":67.5,"y":86.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 67.5, y: 86}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Light Settings
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Camera
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Constraints
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Collisions
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Navmesh Display
      index: 4
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Agent Display
      index: 5
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Obstacle Display
      index: 6
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Occlusion Culling
      index: 5
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Physics Debugger
      index: 7
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Scene Visibility
      index: 8
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Particles
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap
      index: 11
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Palette Helper
      index: 12
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Open Tile Palette
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Focus
      index: 6
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Altos
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Event Tester
      index: 16
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect
      index: 18
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Timeline Control
      index: 19
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Model
      index: 20
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: unity-spline-inspector
      index: 14
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: APV Overlay
      index: 21
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/TrailRenderer
      index: 11
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.SceneViewCameraOverlay
      index: 17
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-camera-mode-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Terrain Tools
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Masks
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 0
      id: Scene View/Lighting Visualization Colors
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 1
      id: Overlays/OverlayMenu
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tile Palette Clipboard
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tile Palette Brush Pick
      index: 9
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: SceneView/CamerasOverlay
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/PBR Validation Settings
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: GPUI Pro
      index: 22
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Visualize Position
      index: 17
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    m_ContainerData:
    - containerId: overlay-toolbar__top
      scrollOffset: 0
    - containerId: overlay-toolbar__left
      scrollOffset: 0
    - containerId: overlay-container--left
      scrollOffset: 0
    - containerId: overlay-container--right
      scrollOffset: 0
    - containerId: overlay-toolbar__right
      scrollOffset: 0
    - containerId: overlay-toolbar__bottom
      scrollOffset: 0
    - containerId: Floating
      scrollOffset: 0
    m_OverlaysVisible: 1
  m_WindowGUID: cc27987af1a868c49b0894db9c0f5429
  m_Gizmos: 1
  m_OverrideSceneCullingMask: 6917529027641081856
  m_SceneIsLit: 1
  m_SceneLighting: 1
  m_2DMode: 0
  m_isRotationLocked: 0
  m_PlayAudio: 0
  m_AudioPlay: 0
  m_DebugDrawModesUseInteractiveLightBakingData: 0
  m_Position:
    m_Target: {x: 0, y: 0, z: 0}
    speed: 2
    m_Value: {x: 0, y: 0, z: 0}
  m_RenderMode: 0
  m_CameraMode:
    drawMode: 0
    name: Shaded
    section: Shading Mode
  m_ValidateTrueMetals: 0
  m_DoValidateTrueMetals: 0
  m_SceneViewState:
    m_AlwaysRefresh: 1
    showFog: 1
    showSkybox: 0
    showFlares: 1
    showImageEffects: 1
    showParticleSystems: 1
    showVisualEffectGraphs: 1
    m_FxEnabled: 1
  m_Grid:
    xGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    yGrid:
      m_Fade:
        m_Target: 1
        speed: 2
        m_Value: 1
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    zGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    m_ShowGrid: 1
    m_GridAxis: 1
    m_gridOpacity: 0.5
  m_Rotation:
    m_Target: {x: 0.065120205, y: -0.90187293, z: 0.33840024, w: 0.26502287}
    speed: 2
    m_Value: {x: -0.06504326, y: 0.9008074, z: -0.33800042, w: -0.26470974}
  m_Size:
    m_Target: 48.28193
    speed: 2
    m_Value: 48.28193
  m_Ortho:
    m_Target: 1
    speed: 2
    m_Value: 1
  m_CameraSettings:
    m_Speed: 1
    m_SpeedNormalized: 0.5
    m_SpeedMin: 0.001
    m_SpeedMax: 2
    m_EasingEnabled: 1
    m_EasingDuration: 0.4
    m_AccelerationEnabled: 1
    m_FieldOfViewHorizontalOrVertical: 60
    m_NearClip: 0.03
    m_FarClip: 10000
    m_DynamicClip: 1
    m_OcclusionCulling: 0
  m_LastSceneViewRotation: {x: 0, y: 0, z: 0, w: 0}
  m_LastSceneViewOrtho: 0
  m_Viewpoint:
    m_SceneView: {fileID: 19}
    m_CameraOverscanSettings:
      m_Opacity: 50
      m_Scale: 1
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_SceneVisActive: 1
  m_LastLockedObject: {fileID: 0}
  m_LastDebugDrawMode:
    drawMode: 35
    name: Contributors / Receivers
    section: Lighting
  m_ViewIsLockedToObject: 0
--- !u!114 &20
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 536
    width: 1271
    height: 450
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders:
    - Assets/_Scripts/Diagnostics
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 16
  m_LastFolders:
  - Assets/_Scripts/Diagnostics
  m_LastFoldersGridSize: 16
  m_LastProjectPath: J:\BTR U6 2025 RG
  m_LockTracker:
    m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 29}
    m_SelectedIDs: 16cc0100
    m_LastClickedID: 117782
    m_ExpandedIDs: 0000000060f90000d2930100d4930100d6930100d8930100da930100dc930100de930100e0930100e2930100e4930100e6930100e8930100ea930100ec930100ee930100f0930100f2930100f4930100f6930100f8930100fa930100fc930100fe93010000940100029401000494010006940100
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 13}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 0000000060f90000d2930100d4930100d6930100d8930100da930100dc930100de930100e0930100e2930100e4930100e6930100e8930100ea930100ec930100ee930100f0930100f2930100f4930100f6930100f8930100fa930100fc930100fe93010000940100029401000494010006940100
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 7e3b0100
    m_LastClickedInstanceID: 80766
    m_HadKeyboardFocusLastEvent: 0
    m_ExpandedInstanceIDs: c623000046730000bc73000074730000fc750000aa8e040018200100ca1f01000000000082ef01001893f3ff9e92f3ff98cdf2ff820b000098f90000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 13}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 16
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 356
--- !u!114 &21
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console
    m_Image: {fileID: -4327648978806127646, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Console\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 558
    width: 1271
    height: 370
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &22
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1586788657, guid: a2284c517ee274c19a6ba4c1a8c96fb6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 120}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console Pro
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Console Pro\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 536
    width: 1271
    height: 450
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  logPanelScroll: {x: 0, y: -49.686554}
  logPanelSelectedIndex: -1
  logPanelAutoScroll: 1
  currentFrame: 51365
--- !u!114 &23
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -2667387946076563598, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Inspector\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1273
    y: 24
    width: 647
    height: 904
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: -160
    m_ControlHash: 1412526313
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -1
  m_LastVerticalScrollValue: 0
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
--- !u!114 &24
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 14000, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Undo History
    m_Image: {fileID: -8654612648804037319, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Undo History\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1272
    y: 79
    width: 647
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ShowLatestFirst: 1
--- !u!114 &25
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12079, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 390, y: 390}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Lighting
    m_Image: {fileID: -1347227620855488341, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Lighting\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1431
    y: 79
    width: 488
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
