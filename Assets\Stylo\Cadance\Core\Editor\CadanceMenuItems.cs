using UnityEngine;
using UnityEditor;
using System.IO;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Menu items and shortcuts for Cadance editor functionality.
    /// Provides easy access to all Cadance tools and utilities.
    /// </summary>
    public static class CadanceMenuItems
    {
        private const string MENU_ROOT = "Stylo/Cadance/";
        private const int PRIORITY_EDITORS = 1;
        private const int PRIORITY_ASSETS = 50;
        private const int PRIORITY_TOOLS = 100;
        private const int PRIORITY_HELP = 200;

        // Editor Windows
        [MenuItem(MENU_ROOT + "Cadance Editor", false, PRIORITY_EDITORS)]
        public static void OpenCadanceEditor()
        {
            CadanceEditorWindow.ShowWindow();
        }

        [MenuItem(MENU_ROOT + "MIDI Converter", false, PRIORITY_EDITORS + 1)]
        public static void OpenMidiConverter()
        {
            CadanceMidiConverterWindow.ShowWindow();
        }

        [MenuItem(MENU_ROOT + "Event Editor", false, PRIORITY_EDITORS + 2)]
        public static void OpenEventEditor()
        {
            CadanceEventEditorWindow.ShowWindow();
        }

        [MenuItem(MENU_ROOT + "MIDI Tempo Editor", false, PRIORITY_EDITORS + 3)]
        public static void OpenMidiTempoEditor()
        {
            CadanceMidiTempoEditor.ShowWindow();
        }

        // Asset Creation
        [MenuItem(MENU_ROOT + "Create/Cadance Asset", false, PRIORITY_ASSETS)]
        public static void CreateCadanceAsset()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "Create Cadance Asset",
                "NewCadance",
                "asset",
                "Create a new Cadance asset");

            if (!string.IsNullOrEmpty(path))
            {
                var cadance = ScriptableObject.CreateInstance<CadanceAsset>();
                cadance.name = Path.GetFileNameWithoutExtension(path);
                AssetDatabase.CreateAsset(cadance, path);
                AssetDatabase.SaveAssets();

                // Open in editor
                CadanceEditorWindow.OpenCadance(cadance);

                Debug.Log($"Created Cadance asset: {path}");
            }
        }

        [MenuItem(MENU_ROOT + "Create/Cadance Track", false, PRIORITY_ASSETS + 1)]
        public static void CreateCadanceTrack()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "Create Cadance Track",
                "NewTrack",
                "asset",
                "Create a new Cadance track");

            if (!string.IsNullOrEmpty(path))
            {
                var track = ScriptableObject.CreateInstance<CadanceTrack>();
                track.name = Path.GetFileNameWithoutExtension(path);
                track.EventID = track.name;
                AssetDatabase.CreateAsset(track, path);
                AssetDatabase.SaveAssets();

                Debug.Log($"Created Cadance track: {path}");
            }
        }

        // Tools and Utilities
        [MenuItem(MENU_ROOT + "Tools/Asset Conversion Tool", false, PRIORITY_TOOLS)]
        public static void OpenAssetConversionTool()
        {
            CadanceAssetConversionTool.ShowWindow();
        }

        [MenuItem(MENU_ROOT + "Tools/Migration/Component Migration Tool", false, PRIORITY_TOOLS + 1)]
        public static void OpenComponentMigrationTool()
        {
            CadanceComponentMigrationTool.ShowWindow();
        }

        [MenuItem(MENU_ROOT + "Tools/Migration/Code Migration Utility", false, PRIORITY_TOOLS + 2)]
        public static void OpenCodeMigrationUtility()
        {
            CadanceCodeMigrationUtility.ShowWindow();
        }

        [MenuItem(MENU_ROOT + "Tools/Migration/Koreographer Inventory", false, PRIORITY_TOOLS + 3)]
        public static void OpenKoreographerInventory()
        {
            KoreographerInventoryTool.ShowWindow();
        }

        [MenuItem(MENU_ROOT + "Tools/Validate Audio Clips", false, PRIORITY_TOOLS + 10)]
        public static void ValidateAudioClips()
        {
            ValidateAudioClipsForCadance();
        }

        [MenuItem(MENU_ROOT + "Tools/Performance Monitor", false, PRIORITY_TOOLS + 11)]
        public static void OpenPerformanceMonitor()
        {
            // Open performance monitor if available
            Debug.Log("Performance monitor functionality would be opened here.");
        }

        // Context menu items for assets
        [MenuItem("Assets/Create/Stylo/Cadance Asset", false, 81)]
        public static void CreateCadanceAssetFromContext()
        {
            CreateCadanceAsset();
        }

        [MenuItem("Assets/Create/Stylo/Cadance Track", false, 82)]
        public static void CreateCadanceTrackFromContext()
        {
            CreateCadanceTrack();
        }

        // Context menu for opening Cadance assets
        [MenuItem("Assets/Open in Cadance Editor", true)]
        public static bool ValidateOpenInCadanceEditor()
        {
            return Selection.activeObject is CadanceAsset;
        }

        [MenuItem("Assets/Open in Cadance Editor", false)]
        public static void OpenInCadanceEditor()
        {
            if (Selection.activeObject is CadanceAsset cadance)
            {
                CadanceEditorWindow.OpenCadance(cadance);
            }
        }

        // Help and Documentation
        [MenuItem(MENU_ROOT + "Help/Documentation", false, PRIORITY_HELP)]
        public static void OpenDocumentation()
        {
            string docPath = "Assets/Stylo/Cadance/Documentation/README.md";
            if (File.Exists(docPath))
            {
                Application.OpenURL("file://" + Path.GetFullPath(docPath));
            }
            else
            {
                EditorUtility.DisplayDialog("Documentation",
                    "Documentation not found. Please check the Cadance installation.", "OK");
            }
        }

        [MenuItem(MENU_ROOT + "Help/Feature Parity Guide", false, PRIORITY_HELP + 1)]
        public static void OpenFeatureParityGuide()
        {
            string guidePath = "Assets/Stylo/Cadance/Documentation/FEATURE_PARITY_IMPLEMENTATION.md";
            if (File.Exists(guidePath))
            {
                Application.OpenURL("file://" + Path.GetFullPath(guidePath));
            }
            else
            {
                EditorUtility.DisplayDialog("Feature Parity Guide",
                    "Feature parity guide not found. Please check the Cadance installation.", "OK");
            }
        }

        [MenuItem(MENU_ROOT + "Help/About Cadance", false, PRIORITY_HELP + 10)]
        public static void ShowAboutDialog()
        {
            EditorUtility.DisplayDialog("About Cadance",
                "Cadance - Advanced Audio Choreography System\n\n" +
                "A complete replacement for Koreographer with enhanced features:\n" +
                "• Full timeline editor with waveform display\n" +
                "• MIDI import and conversion tools\n" +
                "• Advanced FMOD integration\n" +
                "• Real-time beat detection\n" +
                "• Performance monitoring\n" +
                "• One-to-one API compatibility\n\n" +
                "Version: 1.0.0\n" +
                "Developed by: Stylo Systems",
                "OK");
        }

        // Utility methods
        private static void ValidateAudioClipsForCadance()
        {
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
            int validClips = 0;
            int invalidClips = 0;

            foreach (string guid in audioGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AudioClip clip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);

                if (clip != null)
                {
                    if (IsAudioClipValidForCadance(clip))
                    {
                        validClips++;
                    }
                    else
                    {
                        invalidClips++;
                        Debug.LogWarning($"Audio clip '{clip.name}' at '{path}' may not work properly with Cadance. " +
                            "Consider changing import settings to enable 'Load in Background' and set 'Load Type' to 'Decompress On Load'.");
                    }
                }
            }

            EditorUtility.DisplayDialog("Audio Clip Validation",
                $"Validation complete:\n\n" +
                $"Valid clips: {validClips}\n" +
                $"Invalid clips: {invalidClips}\n\n" +
                $"Check the console for details about invalid clips.",
                "OK");
        }

        private static bool IsAudioClipValidForCadance(AudioClip clip)
        {
            // Basic validation - in full implementation would check import settings
            return clip != null && clip.loadType != AudioClipLoadType.Streaming;
        }

        // Keyboard shortcuts (removed to prevent menu conflicts)
        // [MenuItem(MENU_ROOT + "Cadance Editor %&c", false, PRIORITY_EDITORS)]
        // public static void OpenCadanceEditorShortcut()
        // {
        //     OpenCadanceEditor();
        // }

        // [MenuItem(MENU_ROOT + "MIDI Converter %&m", false, PRIORITY_EDITORS + 1)]
        // public static void OpenMidiConverterShortcut()
        // {
        //     OpenMidiConverter();
        // }
    }
}
