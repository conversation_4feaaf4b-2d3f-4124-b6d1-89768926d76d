using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using System;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Editor utility for modifying MIDI tempo metadata without affecting note placements
    /// </summary>
    public class CadanceMidiTempoEditor : EditorWindow
    {
        // MIDI file data
        private string midiFilePath;
        private byte[] midiFileData;
        private float currentTempo = 120f;
        private float newTempo = 120f;
        private bool midiFileLoaded = false;
        private string statusMessage = "";

        // UI state
        private Vector2 scrollPosition;
        private bool showHexView = false;
        private string midiFileName = "";

        public static void ShowWindow()
        {
            CadanceMidiTempoEditor window = GetWindow<CadanceMidiTempoEditor>("MIDI Tempo Editor");
            window.minSize = new Vector2(400, 300);
            window.Show();
        }

        private void OnGUI()
        {
            EditorGUILayout.Space(10);
            
            // Header
            GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel);
            titleStyle.fontSize = 16;
            titleStyle.alignment = TextAnchor.MiddleCenter;
            EditorGUILayout.LabelField("Cadance MIDI Tempo Editor", titleStyle);
            
            EditorGUILayout.Space(10);
            EditorGUILayout.HelpBox("This tool allows you to modify the tempo metadata in MIDI files without affecting note placements.", MessageType.Info);
            EditorGUILayout.Space(10);

            // File selection area
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("MIDI File", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (midiFileLoaded)
            {
                EditorGUILayout.LabelField(midiFileName, EditorStyles.wordWrappedLabel);
            }
            else
            {
                EditorGUILayout.LabelField("No file selected", EditorStyles.wordWrappedLabel);
            }
            
            if (GUILayout.Button("Select File", GUILayout.Width(100)))
            {
                SelectMidiFile();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.Space(10);
            
            // Only show tempo editing if a MIDI file is loaded
            if (midiFileLoaded)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                
                EditorGUILayout.LabelField("Tempo Information", EditorStyles.boldLabel);
                
                // Current tempo display
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Current Tempo:", GUILayout.Width(100));
                EditorGUILayout.LabelField($"{currentTempo} BPM", EditorStyles.boldLabel);
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.Space(5);
                
                // New tempo input
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("New Tempo:", GUILayout.Width(100));
                newTempo = EditorGUILayout.Slider(newTempo, 20f, 300f);
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.Space(5);
                
                // Action buttons
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace();
                
                if (GUILayout.Button("Apply Tempo Change", GUILayout.Width(150)))
                {
                    ApplyTempoChange();
                }
                
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.EndVertical();
                
                EditorGUILayout.Space(10);
                
                // Optional: Hex view toggle
                showHexView = EditorGUILayout.Foldout(showHexView, "MIDI File Hex View", true);
                
                if (showHexView)
                {
                    EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                    scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(150));
                    
                    // Simple hex view of the file
                    string hexView = GetHexView(midiFileData, 16);
                    EditorGUILayout.TextArea(hexView, GUILayout.ExpandHeight(true));
                    
                    EditorGUILayout.EndScrollView();
                    EditorGUILayout.EndVertical();
                }
            }
            
            // Status message at bottom
            if (!string.IsNullOrEmpty(statusMessage))
            {
                EditorGUILayout.Space(10);
                EditorGUILayout.HelpBox(statusMessage, MessageType.Info);
            }
        }

        private void SelectMidiFile()
        {
            string path = EditorUtility.OpenFilePanel("Select MIDI File", "", "mid,midi");
            
            if (string.IsNullOrEmpty(path))
                return;
                
            try
            {
                midiFileData = File.ReadAllBytes(path);
                midiFilePath = path;
                midiFileName = Path.GetFileName(path);
                midiFileLoaded = true;
                
                // Try to extract the tempo from the MIDI file
                currentTempo = ExtractTempoFromMidiFile(midiFileData);
                newTempo = currentTempo;
                
                statusMessage = $"MIDI file loaded. Detected tempo: {currentTempo} BPM";
            }
            catch (Exception ex)
            {
                midiFileLoaded = false;
                statusMessage = $"Error loading MIDI file: {ex.Message}";
                Debug.LogError($"Error loading MIDI file: {ex.Message}");
            }
            
            Repaint();
        }

        private void ApplyTempoChange()
        {
            if (!midiFileLoaded || midiFileData == null)
            {
                statusMessage = "No MIDI file loaded.";
                return;
            }
            
            try
            {
                // Create modified MIDI data with the new tempo
                byte[] modifiedMidiData = ModifyMidiTempo(midiFileData, newTempo);
                
                // Write the modified data back to the file without creating a backup
                File.WriteAllBytes(midiFilePath, modifiedMidiData);
                
                // Update current tempo
                currentTempo = newTempo;
                
                statusMessage = $"Tempo successfully changed to {newTempo} BPM";
                
                // Refresh the AssetDatabase if the file is inside the project
                string projectPath = Application.dataPath;
                projectPath = projectPath.Substring(0, projectPath.Length - 7); // Remove "Assets/"
                
                if (midiFilePath.StartsWith(projectPath))
                {
                    string relativePath = midiFilePath.Substring(projectPath.Length);
                    if (relativePath.StartsWith("/") || relativePath.StartsWith("\\"))
                    {
                        relativePath = relativePath.Substring(1);
                    }
                    
                    AssetDatabase.ImportAsset(relativePath);
                    AssetDatabase.Refresh();
                }
            }
            catch (Exception ex)
            {
                statusMessage = $"Error modifying MIDI file: {ex.Message}";
                Debug.LogError($"Error modifying MIDI file: {ex.Message}");
            }
        }

        private float ExtractTempoFromMidiFile(byte[] midiData)
        {
            try
            {
                // Basic check for MIDI header
                if (midiData.Length < 14 || 
                    midiData[0] != 0x4D || // 'M'
                    midiData[1] != 0x54 || // 'T'
                    midiData[2] != 0x68 || // 'h'
                    midiData[3] != 0x64)   // 'd'
                {
                    Debug.LogWarning("Invalid MIDI file header");
                    return 120f; // Return default tempo
                }
                
                // Search for tempo meta event (FF 51 03 tt tt tt) in the file
                // Where tt tt tt is a 3-byte value representing microseconds per quarter note
                for (int i = 14; i < midiData.Length - 6; i++)
                {
                    if (midiData[i] == 0xFF && midiData[i + 1] == 0x51 && midiData[i + 2] == 0x03)
                    {
                        // Found a tempo meta event
                        int microsecondsPerQuarterNote = (midiData[i + 3] << 16) | (midiData[i + 4] << 8) | midiData[i + 5];
                        
                        // Convert to BPM: 60,000,000 / microseconds per quarter note
                        float bpm = 60000000f / microsecondsPerQuarterNote;
                        return (float)Math.Round(bpm, 2);
                    }
                }
                
                // If no tempo meta event found, return default tempo
                Debug.LogWarning("No tempo meta event found in MIDI file");
                return 120f;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error extracting tempo: {ex.Message}");
                return 120f; // Return default tempo on error
            }
        }

        private byte[] ModifyMidiTempo(byte[] originalMidiData, float newBpm)
        {
            try
            {
                byte[] modifiedData = new byte[originalMidiData.Length];
                originalMidiData.CopyTo(modifiedData, 0);
                
                // Convert BPM to microseconds per quarter note
                int microsecondsPerQuarterNote = (int)(60000000f / newBpm);
                
                // Create the 3-byte tempo value
                byte[] tempoBytes = new byte[3];
                tempoBytes[0] = (byte)((microsecondsPerQuarterNote >> 16) & 0xFF);
                tempoBytes[1] = (byte)((microsecondsPerQuarterNote >> 8) & 0xFF);
                tempoBytes[2] = (byte)(microsecondsPerQuarterNote & 0xFF);
                
                // Search for tempo meta events and replace them
                bool foundTempoEvent = false;
                
                for (int i = 14; i < modifiedData.Length - 6; i++)
                {
                    if (modifiedData[i] == 0xFF && modifiedData[i + 1] == 0x51 && modifiedData[i + 2] == 0x03)
                    {
                        // Replace the tempo bytes
                        modifiedData[i + 3] = tempoBytes[0];
                        modifiedData[i + 4] = tempoBytes[1];
                        modifiedData[i + 5] = tempoBytes[2];
                        
                        foundTempoEvent = true;
                        Debug.Log($"Modified tempo event at position {i} to {newBpm} BPM");
                    }
                }
                
                if (!foundTempoEvent)
                {
                    throw new Exception("No tempo meta event found to modify");
                }
                
                return modifiedData;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error modifying tempo: {ex.Message}");
                throw; // Re-throw to be caught by the caller
            }
        }

        private string GetHexView(byte[] data, int bytesPerLine)
        {
            if (data == null || data.Length == 0)
                return "No data";
                
            System.Text.StringBuilder hexView = new System.Text.StringBuilder();
            
            int maxBytesToShow = Math.Min(data.Length, 1024); // Limit display for performance
            
            for (int i = 0; i < maxBytesToShow; i += bytesPerLine)
            {
                // Show position
                hexView.AppendFormat("{0:X8}: ", i);
                
                // Show hex bytes
                for (int j = 0; j < bytesPerLine; j++)
                {
                    if (i + j < maxBytesToShow)
                    {
                        hexView.AppendFormat("{0:X2} ", data[i + j]);
                    }
                    else
                    {
                        hexView.Append("   ");
                    }
                }
                
                // Show ASCII representation
                hexView.Append(" | ");
                for (int j = 0; j < bytesPerLine; j++)
                {
                    if (i + j < maxBytesToShow)
                    {
                        char c = (char)data[i + j];
                        if (c >= 32 && c <= 126) // Printable ASCII
                        {
                            hexView.Append(c);
                        }
                        else
                        {
                            hexView.Append('.');
                        }
                    }
                }
                
                hexView.AppendLine();
            }
            
            if (maxBytesToShow < data.Length)
            {
                hexView.AppendLine("...");
            }
            
            return hexView.ToString();
        }
    }
}
